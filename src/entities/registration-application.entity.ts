import { Entity, EntityRepositoryType, Index, ManyToOne, Property } from '@mikro-orm/core';
import { EntityRepository } from '@mikro-orm/mysql';

import { BaseEntity } from './base.entity.js';
import { User } from './user.entity.js';

export enum RegistrationApplicationTypeEnum {
  KOL = 'kol',
  WHOLESALE = 'wholesale',
}

export enum RegistrationApplicationStatusEnum {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export interface WholesaleApplicationPayload {
  phoneNumber: string;
  email: string;
  agentName: string;
  representativeName: string;
  citizenIdentifyNumber: string;
  taxCode: string;
  businessLicense: string; // File path/URL to uploaded business license
}

export interface KolApplicationPayload {
  phoneNumber: string;
  email: string;
  representativeName: string;
  socialAccountLinks: string[]; // Array of up to 5 social media links
}

@Entity({ tableName: 'registration_applications', repository: () => RegistrationApplicationRepository })
@Index({ properties: ['user'] })
@Index({ properties: ['type', 'status'] })
export class RegistrationApplication extends BaseEntity {
  [EntityRepositoryType]?: RegistrationApplicationRepository;

  @ManyToOne(() => User, { nullable: false, fieldName: 'user_id' })
  user!: User;

  @Property({ type: 'varchar', length: 20 })
  type!: RegistrationApplicationTypeEnum;

  @Property({ type: 'varchar', length: 20, default: 'pending' })
  status!: RegistrationApplicationStatusEnum;

  @Property({ type: 'json' })
  payload!: WholesaleApplicationPayload | KolApplicationPayload;

  @Property({ nullable: true, type: 'text' })
  note?: string;

  @Property({ nullable: true, type: 'datetime', columnType: 'timestamp' })
  reviewedAt?: Date;

  @Property({ nullable: true })
  @Index()
  reviewedBy?: number; // Admin user ID who reviewed the application
}

export class RegistrationApplicationRepository extends EntityRepository<RegistrationApplication> {
  // Custom methods can be added here
}

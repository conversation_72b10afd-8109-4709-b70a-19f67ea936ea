import { Entity, EntityRepository, EntityRepositoryType, Property, ManyToOne } from '@mikro-orm/core';

import { BaseEntity } from './base.entity.js';
import type { RedeemableGift } from './redeemable-gift.entity.js';

@Entity({ tableName: 'redeemable_products', repository: () => RedeemableVendureProductRepository })
export class RedeemableVendureProduct extends BaseEntity {
  [EntityRepositoryType]?: RedeemableVendureProductRepository;

  @ManyToOne('RedeemableGift', { nullable: true })
  redeemableGift?: RedeemableGift;

  @Property({ nullable: false, type: 'varchar', length: 100 })
  productId!: string;

  @Property({ nullable: false, type: 'varchar', length: 100 })
  variantId!: string;

  @Property({ nullable: false, type: 'varchar', length: 255 })
  variantName!: string;

  @Property({ nullable: true, type: 'decimal', precision: 10, scale: 2 })
  variantPrice?: number;

  @Property({ nullable: false, type: 'int' })
  pointsCost!: number;

  @Property({ nullable: true, type: 'varchar', length: 500 })
  imageUrl?: string;

  @Property({ nullable: true, type: 'varchar', length: 100 })
  sku?: string;
}

export class RedeemableVendureProductRepository extends EntityRepository<RedeemableVendureProduct> {}

import { Entity, Property, Index, EntityRepositoryType, type Opt, ManyToOne } from '@mikro-orm/core';
import { EntityRepository } from '@mikro-orm/mysql';

import { BaseEntity } from './base.entity.js';
import { User } from './user.entity.js';

@Entity({ tableName: 'product_points', repository: () => ProductPointRepository })
export class ProductPoint extends BaseEntity {
  [EntityRepositoryType]?: ProductPointRepository;

  @Property({ nullable: false, type: 'varchar', length: 30 })
  @Index()
  sku!: string;

  @Property({ nullable: false, type: 'varchar', length: 250 })
  @Index()
  name!: string;

  @Property({ nullable: false, type: 'decimal' })
  price!: number;

  @Property({ nullable: true, type: 'int' })
  point?: number;

  @ManyToOne(() => User, { nullable: true, fieldName: 'updated_by' })
  updatedBy?: User;

  @Property({ nullable: true, type: 'datetime', columnType: 'timestamp', onUpdate: () => new Date() })
  override updatedAt!: Date & Opt;
}

export class ProductPointRepository extends EntityRepository<ProductPoint> {
  // your custom methods...
}

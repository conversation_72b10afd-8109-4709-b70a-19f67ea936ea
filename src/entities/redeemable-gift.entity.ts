import { Entity, EntityRepository, EntityRepositoryType, Property, OneToMany, Collection } from '@mikro-orm/core';

import { BaseEntity } from './base.entity.js';
import { RedeemableVendureProduct } from './redeemable-product.entity.js';

@Entity({ tableName: 'redeemable_gifts', repository: () => RedeemableGiftRepository })
export class RedeemableGift extends BaseEntity {
  [EntityRepositoryType]?: RedeemableGiftRepository;

  @Property({ nullable: false, type: 'varchar', length: 30 })
  type!: string; // Gift type, e.g., "Đổi sản phẩm"

  @Property({ nullable: false, type: 'varchar', length: 70 })
  name!: string; // Gift name/title

  @Property({ nullable: true, type: 'text' })
  description?: string;

  @Property({ nullable: false, type: 'tinyint', default: 1 })
  isActive!: boolean;

  @OneToMany({ entity: () => RedeemableVendureProduct, mappedBy: 'redeemableGift', orphanRemoval: true })
  products = new Collection<RedeemableVendureProduct>(this);
}

export class RedeemableGiftRepository extends EntityRepository<RedeemableGift> {}

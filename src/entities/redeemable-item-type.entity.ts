import { Entity, Property, Index, EntityRepositoryType, Collection, OneToMany } from '@mikro-orm/core';
import { EntityRepository } from '@mikro-orm/mysql';

import { BaseEntity } from './base.entity.js';
import { RedeemableItem } from './redeemable-item.entity.js';

@Entity({ tableName: 'redeemable_item_types', repository: () => RedeemableItemTypeRepository })
export class RedeemableItemType extends BaseEntity {
  [EntityRepositoryType]?: RedeemableItemTypeRepository;

  @Property({ nullable: false, type: 'varchar', length: 30, unique: true })
  @Index()
  type!: string;

  @Property({ nullable: false, type: 'varchar', length: 250 })
  name!: string;

  @OneToMany({ entity: () => RedeemableItem, mappedBy: 'type', orphanRemoval: true })
  items = new Collection<RedeemableItem>(this);
}

export class RedeemableItemTypeRepository extends EntityRepository<RedeemableItemType> {
  // your custom methods...
}

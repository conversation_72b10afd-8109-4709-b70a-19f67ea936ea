import { BeforeCreate, BeforeUpdate, Entity, EntityRepositoryType, Index, Property } from '@mikro-orm/core';
import { EntityRepository } from '@mikro-orm/mysql';
import * as bcrypt from 'bcrypt';

import { BaseEntity } from './base.entity.js';
import type { UserRoleEnum, UserStatusEnum, UserTypeEnum } from '../common/enums/index.js';

@Entity({ tableName: 'users', repository: () => UserRepository })
@Index({ properties: ['email', 'password'] })
@Index({ properties: ['phone', 'deleteFlag', 'phoneVerified'] })
export class User extends BaseEntity {
  [EntityRepositoryType]?: UserRepository;

  @Property({ nullable: true })
  @Index()
  email?: string;

  @Property()
  @Index()
  phone!: string;

  @Property({ nullable: true, hidden: true })
  password?: string;

  @Property({ nullable: true, type: 'varchar', length: 250 })
  fullName?: string;

  @Property({ nullable: true })
  avatar?: string;

  @Property({ nullable: true, type: 'varchar', length: 500 })
  address?: string;

  @Property({ nullable: true, type: 'varchar', length: 30 })
  dob?: string;

  @Property({ nullable: true, type: 'varchar', length: 10 })
  gender?: string;

  @Property({ type: 'tinyint', default: 1 })
  status?: UserStatusEnum;

  @Property({ type: 'tinyint', default: 0 })
  phoneVerified?: number;

  @Property({ type: 'tinyint', default: 0 })
  emailVerified?: number;

  // 0: user, 1: admin, 2: super admin
  @Property({ type: 'tinyint', default: 0 })
  role?: UserRoleEnum;

  @Property({ type: 'varchar', length: 20, default: 'retail' })
  type?: UserTypeEnum;

  @Property({ nullable: true, type: 'tinyint', default: 0 })
  deleteFlag?: number;

  @Property({ nullable: true, type: 'datetime', columnType: 'timestamp' })
  deletedAt?: Date;

  @Property({ nullable: true, type: 'varchar', length: 36, unique: true })
  @Index()
  ssoUserId?: string;

  @BeforeCreate()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, 10);
    }
  }

  async comparePassword(password: string): Promise<boolean> {
    if (!this.password) {
      return false;
    }
    return bcrypt.compare(password, this.password);
  }
}

export class UserRepository extends EntityRepository<User> {
  // your custom methods...
}

import { Entity, Property, EntityRepositoryType, ManyToOne } from '@mikro-orm/core';
import { EntityRepository } from '@mikro-orm/mysql';

import { BaseEntity } from './base.entity.js';
import { RedeemableItem } from './index.js';
import type { BooleanEnum } from '../common/enums/boolean.enum.js';

@Entity({ tableName: 'user_redeemable_items', repository: () => UserRedeemableItemRepository })
export class UserRedeemableItem extends BaseEntity {
  [EntityRepositoryType]?: UserRedeemableItemRepository;

  @Property({ nullable: false })
  userId!: number;

  @ManyToOne({ entity: () => RedeemableItem, fieldName: 'redeemable_item_id' })
  redeemableItem!: RedeemableItem;

  @Property({ nullable: false, type: 'varchar', length: 30 })
  code!: string;

  @Property({ nullable: false, type: 'tinyint', default: 0 })
  isUsed?: BooleanEnum; // 0: Not used, 1: Used
}

export class UserRedeemableItemRepository extends EntityRepository<UserRedeemableItem> {
  // your custom methods...
}

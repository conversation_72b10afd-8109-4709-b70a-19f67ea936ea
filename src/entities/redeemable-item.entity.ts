import { Entity, Property, EntityRepositoryType, ManyToOne } from '@mikro-orm/core';
import { EntityRepository } from '@mikro-orm/mysql';

import { BaseEntity } from './base.entity.js';
import type { RedeemableItemType } from './redeemable-item-type.entity.js';

@Entity({ tableName: 'redeemable_items', repository: () => RedeemableItemRepository })
export class RedeemableItem extends BaseEntity {
  [EntityRepositoryType]?: RedeemableItemRepository;

  @ManyToOne('RedeemableItemType')
  type!: RedeemableItemType;

  @Property({ nullable: false, type: 'varchar', length: 250 })
  name!: string;

  @Property({ nullable: false, type: 'varchar', length: 250 })
  description!: string;

  @Property({ nullable: true, type: 'varchar', length: 250 })
  thumbnail!: string;

  @Property({ nullable: true, type: 'int' })
  point!: number;

  @Property({ nullable: false, type: 'bigint' })
  value!: number;
}

export class RedeemableItemRepository extends EntityRepository<RedeemableItem> {
  // your custom methods...
}

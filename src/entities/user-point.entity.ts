import { Entity, EntityRepository, EntityRepositoryType, Index, PrimaryKey, Property, type Opt } from '@mikro-orm/core';

@Entity({ tableName: 'user_point', repository: () => UserPointRepository })
export class UserPoint {
  [EntityRepositoryType]?: UserPointRepository;

  @PrimaryKey()
  id!: number;

  @Property({ nullable: false, unique: true })
  @Index()
  userId!: number;

  @Property({ nullable: false, type: 'int', default: 0 })
  points!: number;

  @Property({ type: 'datetime', columnType: 'timestamp', defaultRaw: `CURRENT_TIMESTAMP`, onUpdate: () => new Date() })
  updatedAt!: Date & Opt;
}

export class UserPointRepository extends EntityRepository<UserPoint> {
  // your custom methods...
}

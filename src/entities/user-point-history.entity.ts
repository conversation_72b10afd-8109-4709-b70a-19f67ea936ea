import { Entity, EntityRepository, EntityRepositoryType, Index, Property } from '@mikro-orm/core';

import { BaseEntity } from './base.entity.js';

@Entity({ tableName: 'user_point_histories', repository: () => UserPointHistoryRepository })
export class UserPointHistory extends BaseEntity {
  [EntityRepositoryType]?: UserPointHistoryRepository;

  @Property({ nullable: false })
  @Index()
  userId!: number;

  @Property({ nullable: false, type: 'varchar', length: 250 })
  itemName!: string;

  @Property({ nullable: false, type: 'int', default: 0 })
  points!: number;

  @Property({ nullable: true })
  redeemableItemId?: number;

  @Property({ nullable: true })
  productQrCodeId?: number;
}

export class UserPointHistoryRepository extends EntityRepository<UserPointHistory> {
  // your custom methods...
}

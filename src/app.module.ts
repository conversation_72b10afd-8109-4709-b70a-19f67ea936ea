import type { MikroORMOptions } from '@mikro-orm/core';
import { MySqlDriver } from '@mikro-orm/mysql';
import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module, type MiddlewareConsumer, type NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ServeStaticModule } from '@nestjs/serve-static';
import { LoggerModule } from 'nestjs-pino';

import { CommonModule, LoggerContextMiddleware } from './common/index.js';
import { SSOModule } from './common/sso/sso.module.js';
import { configuration, loggerOptions } from './config/index.js';
import { AuthModule } from './modules/auth/auth.module.js';
import { HealthModule } from './modules/health/health.module.js';
import { OtpModule } from './modules/otp/otp.module.js';
import { PointManagementModule } from './modules/point-management/point-management.module.js';
import { RedeemableItemModule } from './modules/redeemable-item/redeemable-item.module.js';
import { RegistrationApplicationModule } from './modules/registration-application/registration-application.module.js';
import { StorageModule } from './modules/storage/storage.module.js';
import { UserModule } from './modules/user/user.module.js';
import { VendureRedemptionModule } from './modules/vendure-redemption/vendure-redemption.module.js';

@Module({
  imports: [
    // https://github.com/iamolegga/nestjs-pino
    LoggerModule.forRoot(loggerOptions),
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    // Static
    ServeStaticModule.forRoot({
      rootPath: `${import.meta.dirname}/../public`,
    }),
    /**
     * https://docs.nestjs.com/recipes/mikroorm
     * https://mikro-orm.io/docs/usage-with-nestjs
     * https://mikro-orm.io
     */
    MikroOrmModule.forRootAsync({
      useFactory: (config: ConfigService) => config.getOrThrow<MikroORMOptions>('mikro'),
      inject: [ConfigService],
      driver: MySqlDriver,
    }),
    // Global
    CommonModule,
    // Terminus
    HealthModule,
    // Authentication
    AuthModule,
    // OTP
    OtpModule,
    //  User
    UserModule,
    StorageModule,
    PointManagementModule,
    RedeemableItemModule,
    RegistrationApplicationModule,
    VendureRedemptionModule,
    SSOModule,
  ],
  providers: [
    // {
    //   provide: APP_FILTER,
    //   useClass: ExceptionsFilter,
    // },
    // {
    //   provide: APP_PIPE,
    //   useValue: new ValidationPipe({
    //     transform: true,
    //     whitelist: true,
    //   }),
    // },
  ],
})
export class AppModule implements NestModule {
  // Global Middleware
  public configure(consumer: MiddlewareConsumer): void {
    consumer.apply(LoggerContextMiddleware).forRoutes('*');
  }
}

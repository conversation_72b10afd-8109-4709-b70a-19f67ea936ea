import { IsNotEmpty, IsEnum, IsOptional } from 'class-validator';

import { RegistrationApplicationStatusEnum } from '../../../entities/registration-application.entity.js';

export class ReviewApplicationDto {
  @IsNotEmpty()
  @IsEnum([RegistrationApplicationStatusEnum.APPROVED, RegistrationApplicationStatusEnum.REJECTED])
  readonly status!: RegistrationApplicationStatusEnum.APPROVED | RegistrationApplicationStatusEnum.REJECTED;

  @IsOptional()
  readonly note?: string;
}

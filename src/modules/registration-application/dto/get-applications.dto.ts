import { Transform } from 'class-transformer';
import { IsOptional, IsEnum, IsNumberString } from 'class-validator';

import { RegistrationApplicationStatusEnum, RegistrationApplicationTypeEnum } from '../../../entities/registration-application.entity.js';

export class GetApplicationsDto {
  @IsOptional()
  @IsEnum(RegistrationApplicationStatusEnum)
  readonly status?: RegistrationApplicationStatusEnum;

  @IsOptional()
  @IsEnum(RegistrationApplicationTypeEnum)
  readonly type?: RegistrationApplicationTypeEnum;

  @IsOptional()
  @IsNumberString()
  @Transform(({ value }) => parseInt(value, 10))
  readonly limit?: number = 20;

  @IsOptional()
  @IsNumberString()
  @Transform(({ value }) => parseInt(value, 10))
  readonly offset?: number = 0;
}

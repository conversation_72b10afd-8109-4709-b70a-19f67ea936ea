import { Controller, Get, Put, Param, Body, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { GetUser } from '../../../common/decorators/index.js';
import { UserRoleEnum } from '../../../common/enums/index.js';
import { JwtAuthGuard } from '../../../common/guards/index.js';
import { User } from '../../../entities/user.entity.js';
import { ReviewApplicationDto, GetApplicationsDto } from '../dto/index.js';
import { RegistrationApplicationService } from '../services/index.js';

@ApiTags('registration-applications')
@Controller('registration-applications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RegistrationApplicationController {
  constructor(private readonly registrationApplicationService: RegistrationApplicationService) {}

  @Get('my-applications')
  @ApiOperation({ summary: 'Get current user registration applications' })
  @ApiResponse({ status: 200, description: 'List of user applications' })
  async getMyApplications(@GetUser() user: User) {
    return this.registrationApplicationService.getApplicationsByUser(user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all registration applications (Admin only)' })
  @ApiResponse({ status: 200, description: 'List of all applications' })
  async getAllApplications(@GetUser() user: User, @Query() query: GetApplicationsDto) {
    // Check if user is admin
    if (user.role !== UserRoleEnum.ADMIN && user.role !== UserRoleEnum.SUPER_ADMIN) {
      throw new Error('Unauthorized: Admin access required');
    }

    return this.registrationApplicationService.getAllApplications(query.status, query.type, query.limit, query.offset);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get registration application by ID' })
  @ApiResponse({ status: 200, description: 'Application details' })
  async getApplicationById(@Param('id') id: string, @GetUser() user: User) {
    const application = await this.registrationApplicationService.getApplicationById(parseInt(id, 10));

    if (!application) {
      throw new Error('Application not found');
    }

    // Users can only see their own applications, admins can see all
    if (application.user.id !== user.id && user.role !== UserRoleEnum.ADMIN && user.role !== UserRoleEnum.SUPER_ADMIN) {
      throw new Error('Unauthorized: Cannot access this application');
    }

    return application;
  }

  @Put(':id/review')
  @ApiOperation({ summary: 'Review registration application (Admin only)' })
  @ApiResponse({ status: 200, description: 'Application reviewed successfully' })
  async reviewApplication(@Param('id') id: string, @Body() reviewDto: ReviewApplicationDto, @GetUser() user: User) {
    // Check if user is admin
    if (user.role !== UserRoleEnum.ADMIN && user.role !== UserRoleEnum.SUPER_ADMIN) {
      throw new Error('Unauthorized: Admin access required');
    }

    return this.registrationApplicationService.reviewApplication(parseInt(id, 10), reviewDto.status, user.id, reviewDto.note);
  }

  @Put(':id/cancel')
  @ApiOperation({ summary: 'Cancel registration application' })
  @ApiResponse({ status: 200, description: 'Application cancelled successfully' })
  async cancelApplication(@Param('id') id: string, @GetUser() user: User) {
    return this.registrationApplicationService.cancelApplication(parseInt(id, 10), user.id);
  }
}

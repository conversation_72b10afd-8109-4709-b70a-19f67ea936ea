import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module } from '@nestjs/common';

import { RegistrationApplicationController } from './controllers/registration-application.controller.js';
import { RegistrationApplicationService } from './services/registration-application.service.js';
import { RegistrationApplication } from '../../entities/registration-application.entity.js';

@Module({
  imports: [MikroOrmModule.forFeature([RegistrationApplication])],
  controllers: [RegistrationApplicationController],
  providers: [RegistrationApplicationService],
  exports: [RegistrationApplicationService],
})
export class RegistrationApplicationModule {}

import { EntityManager, type FilterQuery } from '@mikro-orm/core';
import { InjectRepository } from '@mikro-orm/nestjs';
import { Injectable, BadRequestException } from '@nestjs/common';

import { CommonErrorCode } from '../../../common/enums/error-code.enum.js';
import { UserTypeEnum } from '../../../common/enums/index.js';
import {
  RegistrationApplication,
  RegistrationApplicationRepository,
  RegistrationApplicationTypeEnum,
  RegistrationApplicationStatusEnum,
} from '../../../entities/registration-application.entity.js';
import type { WholesaleApplicationPayload, KolApplicationPayload } from '../../../entities/registration-application.entity.js';
import { User } from '../../../entities/user.entity.js';

export interface CreateApplicationDto {
  userId: number;
  type: RegistrationApplicationTypeEnum.KOL | RegistrationApplicationTypeEnum.WHOLESALE;
  payload: WholesaleApplicationPayload | KolApplicationPayload;
}

@Injectable()
export class RegistrationApplicationService {
  constructor(
    @InjectRepository(RegistrationApplication)
    private readonly applicationRepository: RegistrationApplicationRepository,
    private readonly em: EntityManager,
  ) {}

  async createApplication(dto: CreateApplicationDto): Promise<RegistrationApplication> {
    // Check if user already has a pending application of the same type
    const existingApplication = await this.applicationRepository.findOne({
      user: dto.userId,
      type: dto.type,
      status: RegistrationApplicationStatusEnum.PENDING,
    });

    if (existingApplication) {
      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Bạn đã có đơn đăng ký đang chờ duyệt cho loại tài khoản này',
      });
    }

    const userRef = this.em.getReference(User, dto.userId);
    const application = this.applicationRepository.create({
      user: userRef,
      type: dto.type,
      status: RegistrationApplicationStatusEnum.PENDING,
      payload: dto.payload,
    });

    await this.em.persistAndFlush(application);
    return application;
  }

  async getApplicationsByUser(userId: number): Promise<RegistrationApplication[]> {
    return this.applicationRepository.find(
      {
        user: userId,
      },
      {
        orderBy: { createdAt: 'DESC' },
      },
    );
  }

  async getApplicationById(id: number): Promise<RegistrationApplication | null> {
    return this.applicationRepository.findOne({ id }, { populate: ['user'] });
  }

  async getAllApplications(
    status?: RegistrationApplicationStatusEnum,
    type?: RegistrationApplicationTypeEnum,
    limit = 20,
    offset = 0,
  ): Promise<{ applications: RegistrationApplication[]; total: number }> {
    const where: FilterQuery<RegistrationApplication> = {};
    if (status) where.status = status;
    if (type) where.type = type;

    const [applications, total] = await this.applicationRepository.findAndCount(where, {
      populate: ['user'],
      orderBy: { createdAt: 'DESC' },
      limit,
      offset,
    });

    return { applications, total };
  }

  async reviewApplication(
    id: number,
    status: RegistrationApplicationStatusEnum.APPROVED | RegistrationApplicationStatusEnum.REJECTED,
    reviewedBy: number,
    note?: string,
  ): Promise<RegistrationApplication> {
    const application = await this.applicationRepository.findOne({ id }, { populate: ['user'] });

    if (!application) {
      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Không tìm thấy đơn đăng ký',
      });
    }

    if (application.status !== RegistrationApplicationStatusEnum.PENDING) {
      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Đơn đăng ký này đã được xử lý',
      });
    }

    application.status = status;
    application.reviewedBy = reviewedBy;
    application.reviewedAt = new Date();
    if (note) {
      application.note = note;
    }

    // If approved, update user type
    if (status === RegistrationApplicationStatusEnum.APPROVED) {
      const user = application.user;
      user.type = application.type === RegistrationApplicationTypeEnum.KOL ? UserTypeEnum.KOL : UserTypeEnum.WHOLESALE;
      this.em.persist(user);
    }

    await this.em.persistAndFlush(application);
    return application;
  }

  async cancelApplication(id: number, userId: number): Promise<RegistrationApplication> {
    const application = await this.applicationRepository.findOne({
      id,
      user: userId,
      status: RegistrationApplicationStatusEnum.PENDING,
    });

    if (!application) {
      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Không tìm thấy đơn đăng ký hoặc đơn đăng ký không thể hủy',
      });
    }

    application.status = RegistrationApplicationStatusEnum.CANCELLED;
    await this.em.persistAndFlush(application);

    return application;
  }
}

import { Injectable } from '@nestjs/common';

import { S3Service } from '../../../common/s3/s3.service.js';
import type { GetPresignedDto } from '../dto/get-presigned.dto.js';

@Injectable()
export class StorageService {
  constructor(private readonly s3Service: S3Service) {}

  public getUploadUrl(dto: GetPresignedDto): Promise<string> {
    return this.s3Service.createPutObjectPresignedUrl(dto.path, 'image');
  }
}

import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard.js';
import type { GetPresignedDto } from '../dto/index.js';
import { StorageService } from '../services/index.js';

@Controller('storage')
@ApiTags('Storage')
@UseGuards(JwtAuthGuard)
export class StorageController {
  constructor(private readonly storageService: StorageService) {}

  @Post('put-object-url')
  public async register(@Body() dto: GetPresignedDto): Promise<string> {
    return this.storageService.getUploadUrl(dto);
  }
}

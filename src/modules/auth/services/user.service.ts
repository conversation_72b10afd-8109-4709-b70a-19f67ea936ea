import { EntityManager } from '@mikro-orm/mysql';
import { Injectable } from '@nestjs/common';

import { AuthErrorCode, BooleanEnum, UserTypeEnum } from '../../../common/enums/index.js';
import { BadRequestException } from '../../../common/exceptions/index.js';
import { User, UserRepository } from '../../../entities/index.js';
import type { ChangePasswordDto, CreateUserDto } from '../dto/index.js';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private em: EntityManager,
  ) {}

  public async createUser(userData: CreateUserDto): Promise<User> {
    const { phone } = userData;
    const exists = await this.userRepository.count({ phone, deleteFlag: BooleanEnum.FALSE, phoneVerified: BooleanEnum.TRUE });
    if (exists) {
      throw new BadRequestException({
        code: AuthErrorCode.PHONE_ALREADY_REGISTERED,
        message: '<PERSON><PERSON> điện thoại đã được sử dụng',
      });
    }
    // xoa tat ca tai khoan chua hoan thanh luong xac thuc sdt
    await this.userRepository.nativeDelete({
      phone,
      deleteFlag: BooleanEnum.FALSE,
      phoneVerified: BooleanEnum.FALSE,
    });
    const user = this.userRepository.create(userData);
    await this.em.persistAndFlush(user);

    return user;
  }

  async findOneByPhone(phone: string): Promise<User | null> {
    return this.userRepository.findOne({ phone, deleteFlag: BooleanEnum.FALSE });
  }

  async updatePassword(dto: ChangePasswordDto): Promise<void> {
    const user = await this.userRepository.findOneOrFail({
      phone: dto.phone,
      phoneVerified: BooleanEnum.TRUE,
      deleteFlag: BooleanEnum.FALSE,
    });
    this.userRepository.assign(user, { password: dto.password });
    await this.em.flush();
  }

  async findOneById(id: number): Promise<User | null> {
    return this.userRepository.findOne({ id }); // { cache: 60000 }
  }

  public async createUserFromSSO(userData: CreateUserDto & { ssoUserId?: string; type?: UserTypeEnum }): Promise<User> {
    const user = this.userRepository.create({
      phone: userData.phone,
      password: userData.password,
      ssoUserId: userData.ssoUserId,
      type: userData.type ?? UserTypeEnum.RETAIL,
    });

    await this.em.persistAndFlush(user);
    return user;
  }

  public async updateSSOUserId(userId: number, ssoUserId: string): Promise<void> {
    await this.userRepository.nativeUpdate({ id: userId }, { ssoUserId });
  }

  public async findBySSOUserId(ssoUserId: string): Promise<User | null> {
    return this.userRepository.findOne({ ssoUserId, deleteFlag: BooleanEnum.FALSE });
  }
}

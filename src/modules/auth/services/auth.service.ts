import { Injectable, BadRequestException } from '@nestjs/common';

import { UserService } from './user.service.js';
import { AuthErrorCode, CommonErrorCode, UserTypeEnum } from '../../../common/enums/index.js';
import type { User } from '../../../common/interfaces/user.interface.js';
import { SSOClientService } from '../../../common/services/sso-client.service.js';
import type { WholesaleApplicationPayload, KolApplicationPayload } from '../../../entities/registration-application.entity.js';
import { RegistrationApplicationTypeEnum } from '../../../entities/registration-application.entity.js';
import { RegistrationApplicationService } from '../../registration-application/services/index.js';
import type { ChangePasswordDto, ForgotPasswordDto, CreateUserDto } from '../dto/index.js';
import type { UserLoginDto } from '../dto/user-login.dto.js';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly ssoClient: SSOClientService,
    private readonly registrationApplicationService: RegistrationApplicationService,
  ) {}

  public async validateUserLogin(userLogin: UserLoginDto): Promise<{ accessToken: string; user: User }> {
    const ssoResponse = await this.ssoClient.login({
      phone: userLogin.phone,
      password: userLogin.password,
    });

    if (!ssoResponse.accessToken) {
      throw new BadRequestException({
        code: AuthErrorCode.INVALID_CREDENTIALS,
        message: 'Thông tin đăng nhập không chính xác',
      });
    }

    const localUser = await this.syncUserFromSSO(userLogin.phone, ssoResponse.userId);

    return {
      accessToken: ssoResponse.accessToken,
      user: localUser,
    };
  }

  public async forgotPassword(dto: ForgotPasswordDto): Promise<void> {
    await this.ssoClient.forgotPassword({ phone: dto.phone });
  }

  public async changePassword(dto: ChangePasswordDto): Promise<void> {
    const ssoResponse = await this.ssoClient.verifyOtp({
      phone: dto.phone,
      otp: dto.otp,
      type: 'forgot_password',
    });

    if (!ssoResponse.resetToken || typeof ssoResponse.resetToken !== 'string' || ssoResponse.resetToken.trim() === '') {
      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Không thể xác thực OTP, vui lòng thử lại!',
      });
    }

    await this.ssoClient.resetPassword({
      resetToken: ssoResponse.resetToken,
      newPassword: dto.password,
    });
  }

  public async register(userData: CreateUserDto): Promise<string> {
    const ssoResponse = await this.ssoClient.register({
      phone: userData.phone,
      password: userData.password,
    });

    let user: User | null = null;

    if (ssoResponse.accessToken || ssoResponse.otp) {
      user = await this.syncUserFromSSO(userData.phone, ssoResponse.userId, UserTypeEnum.RETAIL);
    }

    if (user && (userData.type === UserTypeEnum.KOL || userData.type === UserTypeEnum.WHOLESALE)) {
      await this.createRegistrationApplication(user, userData);
    }

    if (ssoResponse.otp) {
      return ssoResponse.message ?? 'Mã OTP đã được gửi để xác thực số điện thoại';
    }

    if (userData.type === UserTypeEnum.RETAIL || !userData.type) {
      return ssoResponse.message ?? 'Đăng ký tài khoản thành công';
    } else {
      return (
        'Đăng ký tài khoản thành công. Tài khoản của bạn hiện là loại Retail. Đơn đăng ký ' +
        (userData.type === UserTypeEnum.KOL ? 'KOL' : 'Wholesale') +
        ' sẽ được admin xem xét và phê duyệt để nâng cấp tài khoản.'
      );
    }
  }

  public me(ssoUserId: string): Promise<User | null> {
    return this.userService.findBySSOUserId(ssoUserId);
  }

  private async syncUserFromSSO(phone: string, ssoUserId?: string, userType?: UserTypeEnum): Promise<User> {
    let user = await this.userService.findOneByPhone(phone);

    if (!user) {
      user = await this.userService.createUserFromSSO({
        phone,
        // eslint-disable-next-line sonarjs/no-hardcoded-passwords
        password: 'sso-managed', // Placeholder since SSO manages password
        ssoUserId,
        type: userType ?? UserTypeEnum.RETAIL,
      });
    } else if (ssoUserId && !user.ssoUserId) {
      await this.userService.updateSSOUserId(user.id, ssoUserId);
      user.ssoUserId = ssoUserId;
    }

    return user;
  }

  private async createRegistrationApplication(user: User, userData: CreateUserDto): Promise<void> {
    if (userData.type === UserTypeEnum.WHOLESALE) {
      const payload: WholesaleApplicationPayload = {
        phoneNumber: userData.phone,
        email: userData.email ?? '',
        agentName: userData.agentName ?? '',
        representativeName: userData.representativeName ?? '',
        citizenIdentifyNumber: userData.citizenIdentifyNumber ?? '',
        taxCode: userData.taxCode ?? '',
        businessLicense: userData.businessLicense ?? '',
      };

      await this.registrationApplicationService.createApplication({
        userId: user.id,
        type: RegistrationApplicationTypeEnum.WHOLESALE,
        payload,
      });
    } else if (userData.type === UserTypeEnum.KOL) {
      const payload: KolApplicationPayload = {
        phoneNumber: userData.kolPhoneNumber ?? userData.phone,
        email: userData.kolEmail ?? '',
        representativeName: userData.kolRepresentativeName ?? '',
        socialAccountLinks: userData.socialAccountLinks ?? [],
      };

      await this.registrationApplicationService.createApplication({
        userId: user.id,
        type: RegistrationApplicationTypeEnum.KOL,
        payload,
      });
    }
  }
}

import { Transform } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsEmail, IsEnum, IsArray, ArrayMaxSize, ValidateIf } from 'class-validator';

import { UserTypeEnum } from '../../../common/enums/index.js';

export class CreateUserDto {
  @IsNotEmpty()
  @Transform(({ value }) => value.replace(/\s+/g, ''))
  readonly phone!: string;

  @IsNotEmpty()
  readonly password!: string;

  @IsOptional()
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
  @IsEnum(UserTypeEnum)
  readonly type?: UserTypeEnum;

  // Wholesale specific fields
  @ValidateIf((o) => o.type === UserTypeEnum.WHOLESALE)
  @IsNotEmpty()
  @IsEmail()
  readonly email?: string;

  @ValidateIf((o) => o.type === UserTypeEnum.WHOLESALE)
  @IsNotEmpty()
  readonly agentName?: string;

  @ValidateIf((o) => o.type === UserTypeEnum.WHOLESALE)
  @IsNotEmpty()
  readonly representativeName?: string;

  @ValidateIf((o) => o.type === UserTypeEnum.WHOLESALE)
  @IsNotEmpty()
  readonly citizenIdentifyNumber?: string;

  @ValidateIf((o) => o.type === UserTypeEnum.WHOLESALE)
  @IsNotEmpty()
  readonly taxCode?: string;

  @ValidateIf((o) => o.type === UserTypeEnum.WHOLESALE)
  @IsNotEmpty()
  readonly businessLicense?: string; // File path/URL

  // KOL specific fields
  @ValidateIf((o) => o.type === UserTypeEnum.KOL)
  @IsNotEmpty()
  @IsEmail()
  readonly kolEmail?: string;

  @ValidateIf((o) => o.type === UserTypeEnum.KOL)
  @IsNotEmpty()
  readonly kolRepresentativeName?: string;

  @ValidateIf((o) => o.type === UserTypeEnum.KOL)
  @IsOptional()
  @IsArray()
  @ArrayMaxSize(5)
  readonly socialAccountLinks?: string[];

  // Phone number for KOL (can be same as registration phone)
  @ValidateIf((o) => o.type === UserTypeEnum.KOL)
  @IsOptional()
  @Transform(({ value }) => value?.replace(/\s+/g, ''))
  readonly kolPhoneNumber?: string;
}

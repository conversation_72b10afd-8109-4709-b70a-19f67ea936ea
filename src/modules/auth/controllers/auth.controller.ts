import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import type { FastifyRequest } from 'fastify';

import { LocalAuthGuard } from '../../../common/guards/local-auth.guard.js';
import type { User } from '../../../common/interfaces/index.js';
import type { ChangePasswordDto, CreateUserDto, ForgotPasswordDto } from '../dto/index.js';
import { AuthService } from '../services/index.js';

interface UserWithSSOToken extends User {
  ssoAccessToken: string;
}

@Controller('auth')
@ApiTags('Auth')
export class AuthController {
  // private readonly logger: Logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Post('register')
  public async register(@Body() userData: CreateUserDto): Promise<string> {
    return await this.authService.register(userData);
  }

  @Post('login')
  @UseGuards(LocalAuthGuard)
  public login(@Req() req: FastifyRequest): { accessToken: string } {
    const user = <UserWithSSOToken>req.user;

    return {
      accessToken: user.ssoAccessToken,
    };
  }

  @Post('forgot-password')
  public async forgotPassword(@Body() dto: ForgotPasswordDto): Promise<string> {
    await this.authService.forgotPassword(dto);
    return 'Mã xác thực đã được gửi đến số điện thoại của bạn. Vui lòng kiểm tra tin nhắn!';
  }

  @Post('change-password')
  public async changePassword(@Body() dto: ChangePasswordDto): Promise<void> {
    await this.authService.changePassword(dto);
  }
}

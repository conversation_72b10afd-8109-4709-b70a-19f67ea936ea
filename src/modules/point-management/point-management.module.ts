import { MikroOrmModule } from '@mikro-orm/nestjs';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { PointManagementController } from './controllers/point-management.controller.js';
import { DrceuticsService, QrCodeService } from './services/index.js';
import { ProductPoint, ProductQrCode, UserPoint, UserPointHistory } from '../../entities/index.js';

@Module({
  imports: [HttpModule, MikroOrmModule.forFeature([ProductPoint, ProductQrCode, UserPoint, UserPointHistory])],
  controllers: [PointManagementController],
  providers: [DrceuticsService, QrCodeService],
})
export class PointManagementModule {}

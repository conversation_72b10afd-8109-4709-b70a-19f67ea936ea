import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { GetUser } from '../../../common/decorators/index.js';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard.js';
import type { User } from '../../../common/interfaces/index.js';
import type { ApplyQrDto, QrInfoDto } from '../dto/index.js';
import { QrCodeService } from '../services/index.js';

@Controller('point-management')
@ApiTags('Point Management')
@UseGuards(JwtAuthGuard)
export class PointManagementController {
  constructor(private readonly qrCodeService: QrCodeService) {}

  @Post('scan-qr')
  public scanQr(@Body('qrCode') qrCode: string): Promise<QrInfoDto> {
    return this.qrCodeService.scanQr(qrCode);
  }

  @Post('apply-scratch-code')
  public applyScratchCode(@GetUser() user: User, @Body() dto: ApplyQrDto): Promise<{ point: number }> {
    return this.qrCodeService.applyScratchCode(user, dto);
  }
}

import { Body, Controller, Post, UseGuards, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import type { FastifyReply } from 'fastify';

import { GetUser } from '../../../common/decorators/index.js';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard.js';
import type { User } from '../../../common/interfaces/index.js';
import type { ApplyQrDto, QrInfoDto, GenerateQrDto } from '../dto/index.js';
import { QrCodeService } from '../services/index.js';

@Controller('point-management')
@ApiTags('Point Management')
// @UseGuards(JwtAuthGuard)
export class PointManagementController {
  constructor(private readonly qrCodeService: QrCodeService) {}

  @Post('scan-qr')
  public scanQr(@Body('qrCode') qrCode: string): Promise<QrInfoDto> {
    return this.qrCodeService.scanQr(qrCode);
  }

  @Post('apply-scratch-code')
  public applyScratchCode(@GetUser() user: User, @Body() dto: ApplyQrDto): Promise<{ point: number }> {
    return this.qrCodeService.applyScratchCode(user, dto);
  }

  @Post('generate-qr-codes')
  @ApiOperation({
    summary: 'Generate QR codes and export to Excel',
    description: 'Generates unique QR codes and scratch codes, saves them to database, and exports to Excel file',
  })
  @ApiResponse({
    status: 200,
    description: 'Excel file with generated QR codes',
    headers: {
      'Content-Type': {
        description: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
      'Content-Disposition': {
        description: 'attachment; filename="qr-codes.xlsx"',
      },
    },
  })
  public async generateQrCodes(@Body() dto: GenerateQrDto, @Res() reply: FastifyReply): Promise<void> {
    const excelBuffer = await this.qrCodeService.generateQrCodes(dto.count);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `qr-codes-${timestamp}.xlsx`;

    reply
      .header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      .header('Content-Disposition', `attachment; filename="${filename}"`)
      .send(excelBuffer);
  }
}

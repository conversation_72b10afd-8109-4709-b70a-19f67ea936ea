import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { AxiosError, AxiosResponse } from 'axios';
import * as https from 'https';
import { catchError, firstValueFrom } from 'rxjs';

import { CommonErrorCode } from '../../../common/enums/index.js';
import { BadRequestException } from '../../../common/exceptions/index.js';
import type { DrCeuticsResponseDto, QrInfoDto } from '../dto/index.js';

@Injectable()
export class DrceuticsService {
  private readonly logger = new Logger(DrceuticsService.name);

  constructor(
    private readonly httpService: HttpService,
    private configService: ConfigService,
  ) {}

  public async getProductInfoByQrCode(qrCode: string): Promise<QrInfoDto | null> {
    const url = this.configService.get<string>('drceuticsApiUrl', '');
    const key = this.configService.get<string>('drceuticsApiKey', '');
    const body = { qrcode: qrCode };
    const configs = {
      headers: {
        Authorization: `Bearer ${key}`,
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false, // Bỏ qua kiểm tra chứng chỉ SSL
      }),
    };

    const request = this.httpService.post<AxiosResponse<DrCeuticsResponseDto[]>>(url, body, configs);
    const { data } = await firstValueFrom(
      request.pipe(
        catchError((error: AxiosError) => {
          this.logger.error('Error when calling DrCeutics API', error.response?.data);
          throw new BadRequestException({
            code: CommonErrorCode.OPS_ERROR,
            message: 'Lỗi không xác định, vui lòng thử lại sau!',
          });
        }),
      ),
    );
    const result = data.data.shift();
    if (!result) {
      return null;
    }

    return {
      sku: result.MaSP,
      name: result.tensp,
      price: +result.giaxuat,
      manufacturer: result.Tennsx,
      batch: result.solo,
      expiresAt: result.handung,
      dispatchDate: result.ngaynhapxuat,
      manufactureDate: result.ngaysanxuat ? new Date(result.ngaysanxuat) : undefined,
    };
  }
}

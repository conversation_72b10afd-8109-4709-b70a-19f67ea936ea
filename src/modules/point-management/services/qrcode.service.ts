import { EntityManager, Transactional } from '@mikro-orm/mysql';
import { Injectable, Logger } from '@nestjs/common';
import * as ExcelJS from 'exceljs';

import { DrceuticsService } from './drceutics.service.js';
import { BooleanEnum, QrCodeErrorCode } from '../../../common/enums/index.js';
import { BadRequestException } from '../../../common/exceptions/index.js';
import type { User } from '../../../common/interfaces/user.interface.js';
import {
  ProductPointRepository,
  ProductQrCodeRepository,
  User as UserEntity,
  UserPointHistoryRepository,
  UserPointRepository,
} from '../../../entities/index.js';
import type { ApplyQrDto, QrInfoDto } from '../dto/index.js';

@Injectable()
export class QrCodeService {
  private readonly logger = new Logger(QrCodeService.name);

  constructor(
    private readonly drceuticsService: DrceuticsService,
    private readonly productRepository: ProductPointRepository,
    private readonly productQrRepository: ProductQrCodeRepository,
    private readonly userPointRepository: UserPointRepository,
    private readonly userPointHistoryRepository: UserPointHistoryRepository,
    private em: EntityManager,
  ) {}

  @Transactional()
  public async scanQr(qrCode: string): Promise<QrInfoDto> {
    const sanitizeCode = qrCode.replace('https://', '');
    const [qrCodeInfo, productInfo] = await Promise.all([
      this.productQrRepository.findOne({ qrCode: sanitizeCode }),
      this.drceuticsService.getProductInfoByQrCode(qrCode),
    ]);
    if (!qrCodeInfo || !productInfo) {
      throw new BadRequestException({
        code: QrCodeErrorCode.QR_CODE_NOT_FOUND,
        message: 'Mã QR không tồn tại',
      });
    }

    const product = await this.productRepository.findOne({ sku: productInfo.sku });
    if (!product) {
      // neu san pham chua ton tai trong he thong thi tao moi san pham
      const productEntity = this.productRepository.create({
        sku: productInfo.sku,
        name: productInfo.name,
        price: productInfo.price,
        point: Math.floor(productInfo.price / 20000),
      });
      await this.em.persistAndFlush(productEntity);
    }

    this.productQrRepository.assign(qrCodeInfo, {
      sku: productInfo.sku,
      name: productInfo.name,
      price: productInfo.price,
      manufacturer: productInfo.manufacturer,
      batch: productInfo.batch,
      expiresAt: productInfo.expiresAt,
      dispatchDate: productInfo.dispatchDate,
      manufactureDate: productInfo.manufactureDate,
      point: product?.point ?? Math.floor(productInfo.price / 20000),
    });
    await this.em.flush();

    return {
      ...productInfo,
      dispatchDate: productInfo.manufactureDate?.toISOString().split('T')[0] ?? productInfo.dispatchDate,
      isUsed: !!qrCodeInfo.isUsed,
    };
  }

  @Transactional()
  public async applyScratchCode(user: User, dto: ApplyQrDto): Promise<{ point: number }> {
    // parse qrCode ve chung 1 format: dstlaboratory.vn/promo/<code>
    const userEntity = await this.em.findOneOrFail(UserEntity, { id: user.id });
    const code = dto.qrCode.replace(/^https?:\/\//, '').replace('dstlaboratory.vn/promo/', '');
    const qrValue = `dstlaboratory.vn/promo/${code}`;
    const qrCodeInfo = await this.productQrRepository.findOne({
      sku: dto.sku,
      qrCode: qrValue,
      scratchCode: dto.scratchCode,
    });
    if (!qrCodeInfo || qrCodeInfo.isUsed) {
      throw new BadRequestException({
        code: QrCodeErrorCode.SCRATCH_CODE_INVALID,
        message: 'Mã sản phẩm của bạn đã được tích điểm hoặc sai mã, bạn vui lòng thử lại!',
      });
    }
    // Prioritize point from qrCodeInfo, fallback to calculated from price
    const point = qrCodeInfo.point ?? Math.floor((qrCodeInfo.price ?? 0) / 20000);
    // update thong tin customer su dung ma cao
    qrCodeInfo.isUsed = BooleanEnum.TRUE;
    qrCodeInfo.usedAt = new Date();
    qrCodeInfo.customer = userEntity;
    await this.em.flush();

    const userPoint = await this.userPointRepository.findOne({ userId: userEntity.id });
    if (!userPoint) {
      const userPointEntity = this.userPointRepository.create({
        userId: userEntity.id,
        points: point,
      });
      await this.em.persistAndFlush(userPointEntity);
    } else {
      userPoint.points += point;
      await this.em.flush();
    }

    // luu lich su su dung ma cao
    const history = this.userPointHistoryRepository.create({
      userId: userEntity.id,
      itemName: qrCodeInfo.name ?? '',
      points: point,
      productQrCodeId: qrCodeInfo.id,
    });
    await this.em.persistAndFlush(history);

    return { point };
  }

  /**
   * Generate unique QR codes and scratch codes
   */
  private generateUniqueCode(length: number, prefix: string = ''): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = prefix;
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Generate unique 9-digit scratch code
   */
  private generateScratchCode(): string {
    return Math.floor(100000000 + Math.random() * 900000000).toString();
  }

  /**
   * Generate bulk QR codes and export to Excel
   */
  @Transactional()
  public async generateQrCodes(count = 210000): Promise<Buffer> {
    const qrPrefix = 'dstlaboratory.vn/promo/DRC25';
    const generatedCodes: { qrCode: string; scratchCode: string; serial: string }[] = [];

    // Get existing QR codes and scratch codes to ensure uniqueness
    const existingQrCodes = new Set(
      (await this.productQrRepository.findAll({ fields: ['qrCode'] })).map((item) => item.qrCode)
    );
    const existingScratchCodes = new Set(
      (await this.productQrRepository.findAll({ fields: ['scratchCode'] })).map((item) => item.scratchCode)
    );

    this.logger.log(`Starting generation of ${count} QR codes...`);
    this.logger.log(`Existing QR codes: ${existingQrCodes.size}`);
    this.logger.log(`Existing scratch codes: ${existingScratchCodes.size}`);

    for (let i = 0; i < count; i++) {
      let qrCode: string;
      let scratchCode: string;
      let serial: string;

      // Generate unique QR code
      do {
        serial = this.generateUniqueCode(8); // Generate 8 character serial like YZP2ES
        qrCode = `${qrPrefix}${serial}`;
      } while (existingQrCodes.has(qrCode));

      // Generate unique scratch code
      do {
        scratchCode = this.generateScratchCode();
      } while (existingScratchCodes.has(scratchCode));

      // Add to sets to prevent duplicates in current batch
      existingQrCodes.add(qrCode);
      existingScratchCodes.add(scratchCode);

      generatedCodes.push({ qrCode, scratchCode, serial });

      // Log progress every 10,000 codes
      if ((i + 1) % 10000 === 0) {
        this.logger.log(`Generated ${i + 1}/${count} codes...`);
      }
    }

    this.logger.log('Saving codes to database...');

    // Save to database in batches
    const batchSize = 1000;
    for (let i = 0; i < generatedCodes.length; i += batchSize) {
      const batch = generatedCodes.slice(i, i + batchSize);
      const entities = batch.map(code =>
        this.productQrRepository.create({
          qrCode: code.qrCode,
          scratchCode: code.scratchCode,
          isUsed: BooleanEnum.FALSE,
        })
      );

      await this.em.persistAndFlush(entities);
      this.logger.log(`Saved batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(generatedCodes.length / batchSize)}`);
    }

    this.logger.log('Creating Excel file...');

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('QR Codes');

    // Add headers
    worksheet.columns = [
      { header: 'QR Code', key: 'qrCode', width: 40 },
      { header: 'Scratch Code', key: 'scratchCode', width: 15 },
      { header: 'Serial', key: 'serial', width: 15 },
    ];

    // Add data
    generatedCodes.forEach(code => {
      worksheet.addRow({
        qrCode: code.qrCode,
        scratchCode: code.scratchCode,
        serial: code.serial,
      });
    });

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    this.logger.log('Excel file created successfully');

    // Return Excel buffer
    return await workbook.xlsx.writeBuffer() as Buffer;
  }
}

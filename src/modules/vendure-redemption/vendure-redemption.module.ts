import { MikroOrmModule } from '@mikro-orm/nestjs';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { VendureRedemptionController } from './controllers/vendure-redemption.controller.js';
import { RedemptionService } from './services/redemption.service.js';
import { VendureProductService } from './services/vendure-product.service.js';
import { UserPoint, UserPointHistory, RedeemableVendureProduct, RedeemableGift } from '../../entities/index.js';

@Module({
  imports: [HttpModule, MikroOrmModule.forFeature([UserPoint, UserPointHistory, RedeemableVendureProduct, RedeemableGift])],
  controllers: [VendureRedemptionController],
  providers: [VendureProductService, RedemptionService],
  exports: [VendureProductService, RedemptionService],
})
export class VendureRedemptionModule {}

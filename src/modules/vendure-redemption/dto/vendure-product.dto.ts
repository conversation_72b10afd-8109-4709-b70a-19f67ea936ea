import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class VendureProductDto {
  @ApiProperty({ description: 'Product ID' })
  id!: number;

  @ApiProperty({ description: 'Vendure product ID' })
  productId!: string;

  @ApiProperty({ description: 'Vendure variant ID' })
  variantId!: string;

  @ApiProperty({ description: 'Product name' })
  variantName!: string;

  @ApiProperty({ description: 'Points required to redeem this product' })
  pointsCost!: number;

  @ApiPropertyOptional({ description: 'Original price in Vendure' })
  variantPrice?: number;

  @ApiPropertyOptional({ description: 'Product image URL' })
  imageUrl?: string;

  @ApiPropertyOptional({ description: 'Product SKU' })
  sku?: string;

  @ApiPropertyOptional({ description: 'Stock level status from Vendure' })
  stockLevel?: string;
}

export class VendureProductListDto {
  @ApiProperty({ type: [VendureProductDto] })
  products!: VendureProductDto[];

  @ApiProperty({ description: 'Total number of products' })
  total!: number;
}

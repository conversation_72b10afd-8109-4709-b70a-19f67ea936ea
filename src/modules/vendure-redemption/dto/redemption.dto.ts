import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsN<PERSON>ber, Min, ValidateNested } from 'class-validator';

export class ShippingAddressDto {
  @ApiProperty({ description: 'Email' })
  @IsString()
  email!: string;

  @ApiProperty({ description: 'Full name' })
  @IsString()
  fullName!: string;

  @ApiProperty({ description: 'Street line 1' })
  @IsString()
  streetLine1!: string;

  @ApiProperty({ description: 'Country code' })
  @IsString()
  countryCode!: string;

  @ApiProperty({ description: 'Phone number' })
  @IsString()
  phoneNumber!: string;

  @ApiProperty({ description: 'District' })
  @IsString()
  district!: string;

  @ApiProperty({ description: 'Ward' })
  @IsString()
  ward!: string;

  @ApiProperty({ description: 'Province' })
  @IsString()
  province!: string;
}

export class RedeemProductDto {
  @ApiProperty({ description: 'Product ID to redeem' })
  @IsNumber()
  productId!: number;

  @ApiProperty({ description: 'Quantity to redeem' })
  @IsNumber()
  @Min(1)
  quantity!: number;

  @ApiProperty({ type: ShippingAddressDto })
  @ValidateNested()
  @Type(() => ShippingAddressDto)
  shippingAddress!: ShippingAddressDto;
}

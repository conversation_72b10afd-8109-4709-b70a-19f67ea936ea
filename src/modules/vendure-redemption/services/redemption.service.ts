import { EntityManager } from '@mikro-orm/mysql';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { catchError, firstValueFrom } from 'rxjs';

import { VendureProductService } from './vendure-product.service.js';
import { RedeemableProductErrorCode } from '../../../common/enums/error-code.enum.js';
import { BadRequestException } from '../../../common/exceptions/index.js';
import { UserPointRepository, UserPointHistoryRepository } from '../../../entities/index.js';
import { RedeemProductDto, CreateOrderResponseDto } from '../dto/index.js';

@Injectable()
export class RedemptionService {
  constructor(
    private readonly em: EntityManager,
    private readonly userPointRepository: UserPointRepository,
    private readonly userPointHistoryRepository: UserPointHistoryRepository,
    private readonly vendureProductService: VendureProductService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async redeemProduct(
    dto: RedeemProductDto,
    userInfo: { id: number; email?: string; fullName?: string; phone?: string },
  ): Promise<CreateOrderResponseDto> {
    const productValidation = await this.vendureProductService.validateProductForRedemption(dto.productId, dto.quantity);

    if (!productValidation.isValid) {
      throw new BadRequestException({
        code: RedeemableProductErrorCode.REDEEMABLE_PRODUCT_NOT_AVAILABLE,
        message: productValidation.errorMessage ?? 'Sản phẩm không khả dụng để đổi điểm',
      });
    }

    const product = productValidation.product;
    if (!product) {
      throw new BadRequestException({
        code: RedeemableProductErrorCode.REDEEMABLE_PRODUCT_NOT_AVAILABLE,
        message: 'Sản phẩm không khả dụng để đổi điểm',
      });
    }
    const totalPointsRequired = product.pointsCost * dto.quantity;

    try {
      await this.em.begin();

      await this.deductUserPoints(userInfo.id, totalPointsRequired);

      await this.createRedemptionHistory(userInfo.id, product.variantName, totalPointsRequired);

      const vendureResult = await this.createVendureOrder(
        {
          id: product.id,
          name: product.variantName,
          pointsCost: product.pointsCost,
          vendureVariantId: product.variantId,
        },
        dto,
        userInfo,
        totalPointsRequired,
      );

      if (!vendureResult.success || !vendureResult.order) {
        throw new BadRequestException({
          code: RedeemableProductErrorCode.VENDURE_ORDER_CREATION_FAILED,
          message: vendureResult.errorMessage ?? 'Lỗi khi tạo đơn hàng.',
        });
      }

      await this.em.commit();
      return vendureResult.order;
    } catch (error) {
      await this.em.rollback();
      throw error;
    }
  }

  private async deductUserPoints(userId: number, pointsToDeduct: number): Promise<void> {
    const userPoint = await this.userPointRepository.findOne({ userId });

    if (!userPoint || userPoint.points < pointsToDeduct) {
      throw new BadRequestException({
        code: RedeemableProductErrorCode.USER_POINT_NOT_ENOUGH,
        message: 'Không đủ điểm.',
      });
    }

    userPoint.points -= pointsToDeduct;
    await this.em.flush();
  }

  private async createRedemptionHistory(userId: number, itemName: string, points: number): Promise<void> {
    const history = this.userPointHistoryRepository.create({
      userId,
      itemName: itemName,
      points: -points,
    });

    await this.em.persistAndFlush(history);
  }

  private async createVendureOrder(
    product: { id: number; name: string; pointsCost: number; vendureVariantId: string },
    dto: RedeemProductDto,
    userInfo: { id: number; email?: string; fullName?: string; phone?: string },
    totalPoints: number,
  ): Promise<{ success: boolean; order: CreateOrderResponseDto | null; errorMessage?: string }> {
    try {
      // Prepare the request for Vendure points redemption API
      const vendureRequest = {
        pointsAppUserId: userInfo.id,
        pointsUsed: totalPoints,
        redemptionToken: `redemption-${Date.now()}-${product.id}`,
        customerData: {
          email: userInfo.email,
          fullName: userInfo.fullName,
          phoneNumber: userInfo.phone,
        },
        shippingAddress: {
          fullName: dto.shippingAddress.fullName,
          streetLine1: dto.shippingAddress.streetLine1,
          countryCode: dto.shippingAddress.countryCode,
          phoneNumber: dto.shippingAddress.phoneNumber,
          province: dto.shippingAddress.province,
          customFields: {
            district: dto.shippingAddress.district,
            ward: dto.shippingAddress.ward,
          },
        },
        items: [
          {
            productVariantId: product.vendureVariantId,
            quantity: dto.quantity,
          },
        ],
      };

      const vendureApiUrl = this.configService.get<string>('vendureApiUrl');
      const channelToken = this.configService.get<string>('vendureChannelToken');
      const headers = {
        'Content-Type': 'application/json',
        'channel-token': channelToken,
      };
      const request$ = this.httpService.post(`${vendureApiUrl}/api/v1/points-redemption/create-order`, vendureRequest, { headers });
      const response = await firstValueFrom(
        request$.pipe(
          catchError((error) => {
            const errorMessage = error.response?.data ? JSON.stringify(error.response.data) : (error.message ?? 'Unknown error');
            return [
              {
                data: {
                  success: false,
                  order: null,
                  error: errorMessage instanceof Error ? errorMessage.message : String(errorMessage),
                },
              },
            ];
          }),
        ),
      );
      const result = response.data;
      return {
        success: result.success,
        order: result.order,
        errorMessage: result.error,
      };
    } catch (error) {
      return {
        success: false,
        order: null,
        errorMessage: `Failed to connect to Vendure: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  async getUserOrderHistory(userId: number, options?: { page?: number; limit?: number; state?: string }): Promise<unknown> {
    const vendureApiUrl = this.configService.get<string>('vendureApiUrl');
    const channelToken = this.configService.get<string>('vendureChannelToken');
    const headers = {
      'Content-Type': 'application/json',
      'channel-token': channelToken,
    };
    const params: Record<string, unknown> = {};
    if (options?.page !== undefined) params['page'] = options.page;
    if (options?.limit !== undefined) params['limit'] = options.limit;
    if (options?.state !== undefined) params['state'] = options.state;
    try {
      const request$ = this.httpService.get(`${vendureApiUrl}/api/v1/points-redemption/user-orders/${userId}`, { headers, params });
      const response = await firstValueFrom(
        request$.pipe(
          catchError((error) => {
            const errorMessage = error.response?.data ? JSON.stringify(error.response.data) : (error.message ?? 'Unknown error');
            throw new BadRequestException({
              code: 400,
              message: errorMessage,
            });
          }),
        ),
      );
      return response.data;
    } catch (error) {
      throw new BadRequestException({
        code: 400,
        message: `Failed to fetch order history: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  }

  async getProductVariantData(variantId: string): Promise<unknown> {
    const vendureApiUrl = this.configService.get<string>('vendureApiUrl');
    const channelToken = this.configService.get<string>('vendureChannelToken');
    const headers = {
      'Content-Type': 'application/json',
      'channel-token': channelToken,
    };
    try {
      const request$ = this.httpService.get(`${vendureApiUrl}/api/v1/points-redemption/product-variant/${variantId}`, { headers });
      const response = await firstValueFrom(
        request$.pipe(
          catchError((error) => {
            const errorMessage = error.response?.data ? JSON.stringify(error.response.data) : (error.message ?? 'Unknown error');
            throw new BadRequestException({
              code: 400,
              message: errorMessage,
            });
          }),
        ),
      );
      return response.data;
    } catch (error) {
      throw new BadRequestException({
        code: 400,
        message: `Failed to fetch product variant data: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  }
}

import { HttpService } from '@nestjs/axios';
import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosError } from 'axios';
import { firstValueFrom } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { RedeemableVendureProductRepository } from '../../../entities/index.js';
import { VendureProductDto, VendureProductListDto } from '../dto/vendure-product.dto.js';

interface VendureProductVariantValidationResponse {
  id: string;
  name: string;
  sku: string;
  enabled: boolean;
  price: number;
  stock: number;
  deletedAt?: string | null;
  product: {
    id: string;
    name: string;
    enabled: boolean;
    deletedAt?: string | null;
  };
}

interface VendureValidationResult {
  isValid: boolean;
  product?: VendureProductDto;
  errorMessage?: string;
}

@Injectable()
export class VendureProductService {
  constructor(
    private readonly redeemableVendureProductRepository: RedeemableVendureProductRepository,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async validateProductForRedemption(productId: number, quantity: number): Promise<VendureValidationResult> {
    const product = await this.redeemableVendureProductRepository.findOne({ id: productId });
    if (!product) return { isValid: false };

    const vendureVariant = await this.fetchVendureProductVariant(product.variantId);
    if (
      !vendureVariant ||
      vendureVariant.deletedAt ||
      !vendureVariant.enabled ||
      !vendureVariant.product.enabled ||
      vendureVariant.product.deletedAt
    ) {
      return { isValid: false, errorMessage: 'Sản phẩm không khả dụng để đổi điểm' };
    }

    if (vendureVariant.stock < quantity) {
      return { isValid: false, errorMessage: 'Không đủ số lượng sản phẩm.' };
    }

    return {
      isValid: true,
      product: {
        id: product.id,
        productId: product.productId,
        variantId: product.variantId,
        variantName: product.variantName,
        variantPrice: product.variantPrice,
        pointsCost: product.pointsCost,
        imageUrl: product.imageUrl,
        sku: product.sku,
      },
    };
  }

  private async fetchVendureProductVariant(vendureVariantId: string): Promise<VendureProductVariantValidationResponse | null> {
    const vendureApiUrl = this.configService.get<string>('vendureApiUrl');
    const channelToken = this.configService.get<string>('vendureChannelToken');
    const request = this.httpService.get(`${vendureApiUrl}/api/v1/products/variant/${vendureVariantId}`, {
      headers: {
        'Content-Type': 'application/json',
        'channel-token': channelToken,
      },
    });
    const response = await firstValueFrom(
      request.pipe(
        catchError((err: AxiosError) => {
          if (err.response?.status === 404) {
            return [null];
          }
          throw new BadRequestException(err instanceof Error ? err.message : String(err));
        }),
      ),
    );
    if (!response || !('data' in response) || !response.data) {
      return null;
    }
    return response.data;
  }

  async findProducts(options?: { search?: string; limit?: number; page?: number }): Promise<VendureProductListDto> {
    const { search, limit = 10, page = 1 } = options ?? {};
    const offset = (page - 1) * limit;

    let whereClause: Record<string, unknown> = {
      redeemableGift: { isActive: true },
    };

    if (search) {
      whereClause = {
        ...whereClause,
        $or: [{ variantName: { $like: `%${search}%` } }, { sku: { $like: `%${search}%` } }],
      };
    }

    const [products, total] = await this.redeemableVendureProductRepository.findAndCount(whereClause, {
      orderBy: { createdAt: 'DESC' },
      limit: 100,
      offset,
      fields: ['id', 'productId', 'variantId', 'variantName', 'variantPrice', 'pointsCost', 'imageUrl', 'sku'],
    });

    if (products.length > 0) {
      const variantIds = products.map((product) => product.variantId);
      const stockAvailability = await this.getStockAvailability(variantIds);

      const productsWithStock = products.map((product) => {
        const stockInfo = stockAvailability.find((stock) => stock.variantId === product.variantId);
        return {
          ...product,
          stockLevel: stockInfo?.stockLevel ?? 'OUT_OF_STOCK',
        };
      });

      return {
        products: productsWithStock,
        total,
      };
    }

    return {
      products,
      total,
    };
  }

  private async getStockAvailability(variantIds: string[]): Promise<{ variantId: string; stockLevel: string }[]> {
    const vendureApiUrl = this.configService.get<string>('vendureApiUrl');
    const channelToken = this.configService.get<string>('vendureChannelToken');

    try {
      const request = this.httpService.post(
        `${vendureApiUrl}/api/v1/points-redemption/stock-availability`,
        { variantIds },
        {
          headers: {
            'Content-Type': 'application/json',
            'channel-token': channelToken,
          },
        },
      );

      const response = await firstValueFrom(
        request.pipe(
          catchError((err: AxiosError) => {
            throw new BadRequestException(`Failed to fetch stock availability: ${err.message}`);
          }),
        ),
      );

      if (response.data?.success && response.data.stockAvailability) {
        return response.data.stockAvailability;
      }

      return [];
    } catch (error) {
      throw new BadRequestException(`Error fetching stock availability: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

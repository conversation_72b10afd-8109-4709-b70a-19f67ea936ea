import { Body, Controller, Get, Post, Query, UseGuards, Param } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

import { GetUser } from '../../../common/decorators/index.js';
import { JwtAuthGuard } from '../../../common/guards/index.js';
import { User } from '../../../entities/index.js';
import { RedeemProductDto, CreateOrderResponseDto } from '../dto/index.js';
import { VendureProductListDto } from '../dto/vendure-product.dto.js';
import { RedemptionService } from '../services/redemption.service.js';
import { VendureProductService } from '../services/vendure-product.service.js';

@ApiTags('Vendure Redemption')
@Controller('vendure-redemption')
@UseGuards(JwtAuthGuard)
export class VendureRedemptionController {
  constructor(
    private readonly vendureProductService: VendureProductService,
    private readonly redemptionService: RedemptionService,
  ) {}

  @Get('products')
  @ApiOperation({ summary: 'Get available products for redemption' })
  @ApiQuery({ name: 'search', required: false, description: 'Search products by name, SKU, or description' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of products per page (default: 20)' })
  @ApiQuery({ name: 'offset', required: false, type: Number, description: 'Number of products to skip (default: 0)' })
  @ApiResponse({ status: 200, type: VendureProductListDto })
  async getProducts(@Query('search') search?: string, @Query('limit') limit = 10, @Query('page') page = 1): Promise<VendureProductListDto> {
    return this.vendureProductService.findProducts({
      search,
      limit,
      page,
    });
  }

  @Post('redeem')
  @ApiOperation({
    summary: 'Redeem a product using points',
    description:
      'Validates user eligibility and redeems product in a single operation. Returns detailed validation and redemption results.',
  })
  @ApiResponse({ status: 200, type: CreateOrderResponseDto })
  async redeemProduct(@GetUser() user: User, @Body() dto: RedeemProductDto): Promise<CreateOrderResponseDto> {
    const userInfo = {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      phone: user.phone,
    };

    return this.redemptionService.redeemProduct(dto, userInfo);
  }

  @Get('order-history')
  @ApiOperation({ summary: 'Get order history for the current user' })
  async getOrderHistory(
    @GetUser() user: User,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('state') state?: string,
  ) {
    return this.redemptionService.getUserOrderHistory(user.id, {
      page: page !== undefined ? Number(page) : undefined,
      limit: limit !== undefined ? Number(limit) : undefined,
      state,
    });
  }

  @Get('product-variant/:variantId')
  @ApiOperation({ summary: 'Get product variant data from Vendure by variantId' })
  async getProductVariant(@Param('variantId') variantId: string) {
    return this.redemptionService.getProductVariantData(variantId);
  }
}

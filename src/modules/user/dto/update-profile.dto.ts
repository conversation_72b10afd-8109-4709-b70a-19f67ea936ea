import { Transform } from 'class-transformer';
import { Is<PERSON>mail, IsNotEmpty } from 'class-validator';

export class UpdateProfileDto {
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  readonly fullName!: string;

  readonly avatar?: string;

  @IsNotEmpty()
  @IsEmail()
  @Transform(({ value }) => value.trim().toLowerCase())
  readonly email!: string;

  @IsNotEmpty()
  readonly dob!: string;

  @IsNotEmpty()
  readonly gender!: string;

  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  readonly address!: string;
}

import { Body, Controller, Get, Put, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { GetUser } from '../../../common/decorators/index.js';
import { JwtAuthGuard } from '../../../common/guards/index.js';
import type { User } from '../../../common/interfaces/index.js';
import type { UpdatePasswordDto, UpdateProfileDto, MyRedeemableItemsDto } from '../dto/index.js';
import { UserService } from '../services/index.js';

@Controller('user')
@ApiTags('User')
@UseGuards(JwtAuthGuard)
export class UserController {
  // private readonly logger: Logger = new Logger(AuthController.name);

  constructor(private readonly userService: UserService) {}

  @Get('me')
  public login(@GetUser() user: User): User {
    return user;
  }

  @Put('profile')
  public async updateProfile(@GetUser() user: User, @Body() dto: UpdateProfileDto): Promise<void> {
    await this.userService.update(user.id, dto);
  }

  @Put('change-password')
  public async updatePassword(@GetUser() user: User, @Body() dto: UpdatePasswordDto): Promise<void> {
    await this.userService.updatePassword(user.id, dto);
  }

  @Get('point-histories')
  public pointHistories(@GetUser() user: User): Promise<{ name?: string; point?: number; date?: Date }[]> {
    return this.userService.getPointHistories(user.id);
  }

  @Get('points')
  public myPoints(@GetUser() user: User): Promise<{ points: number }> {
    return this.userService.getMyPoints(user.id);
  }

  @Get('redeemable-items')
  public myRedeemableItems(@GetUser() user: User): Promise<MyRedeemableItemsDto[]> {
    return this.userService.getMyRedeemableItems(user.id);
  }
}

import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module } from '@nestjs/common';

import { UserController } from './controllers/user.controller.js';
import { UserService } from './services/index.js';
import { SSOModule } from '../../common/sso/sso.module.js';
import { ProductQrCode, User, UserPoint, UserPointHistory, UserRedeemableItem } from '../../entities/index.js';
import { OtpModule } from '../otp/otp.module.js';

@Module({
  imports: [MikroOrmModule.forFeature([User, ProductQrCode, UserPoint, UserPointHistory, UserRedeemableItem]), OtpModule, SSOModule],
  controllers: [UserController],
  providers: [UserService],
  exports: [UserService],
})
export class UserModule {}

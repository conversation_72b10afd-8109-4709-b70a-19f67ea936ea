import { Injectable, BadRequestException } from '@nestjs/common';

import { CommonErrorCode } from '../../../common/enums/index.js';
import { SSOClientService } from '../../../common/services/sso-client.service.js';
import { UserRepository, UserPointRepository, UserPointHistoryRepository, UserRedeemableItemRepository } from '../../../entities/index.js';
import type { UpdateProfileDto, UpdatePasswordDto, MyRedeemableItemsDto } from '../dto/index.js';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly userPointRepository: UserPointRepository,
    private readonly userPointHistoryRepository: UserPointHistoryRepository,
    private readonly userRedeemableItemRepository: UserRedeemableItemRepository,
    private readonly ssoClient: SSOClientService,
  ) {}

  public async update(userId: number, updateData: UpdateProfileDto): Promise<void> {
    await this.userRepository.nativeUpdate({ id: userId }, { ...updateData });
  }

  public async updatePassword(userId: number, dto: UpdatePasswordDto): Promise<void> {
    const user = await this.userRepository.findOneOrFail({ id: userId });

    const ssoResponse = await this.ssoClient.verifyOtp({
      phone: user.phone,
      otp: dto.otp,
      type: 'change_password',
    });

    if (!ssoResponse.resetToken) {
      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Không thể xác thực OTP, vui lòng thử lại!',
      });
    }

    await this.ssoClient.resetPassword({
      resetToken: ssoResponse.resetToken,
      newPassword: dto.newPassword,
    });
  }

  public async getPointHistories(userId: number): Promise<{ name?: string; point?: number; date?: Date }[]> {
    const histories = await this.userPointHistoryRepository.find({ userId }, { orderBy: { createdAt: 'desc' } });
    return histories.map((history) => ({
      name: history.itemName,
      point: history.points,
      date: history.createdAt,
    }));
  }

  public async getMyPoints(userId: number): Promise<{ points: number }> {
    const userPoint = await this.userPointRepository.findOne({ userId });
    return {
      points: userPoint?.points ?? 0,
    };
  }

  public async getMyRedeemableItems(userId: number): Promise<MyRedeemableItemsDto[]> {
    const items = await this.userRedeemableItemRepository.find(
      { userId },
      { populate: ['redeemableItem'], orderBy: { createdAt: 'desc' } },
    );

    return items.map((item) => ({
      id: item.id,
      itemId: item.redeemableItem.id,
      name: item.redeemableItem.name,
      description: item.redeemableItem.description,
      thumbnail: item.redeemableItem.thumbnail,
      point: item.redeemableItem.point,
      value: item.redeemableItem.value,
      code: item.code,
    }));
  }
}

/* eslint-disable sonarjs/pseudo-random */
import { Transactional, EntityManager } from '@mikro-orm/mysql';
import { Injectable } from '@nestjs/common';

import { RedeemableItemErrorCode } from '../../../common/enums/index.js';
import { BadRequestException } from '../../../common/exceptions/index.js';
import {
  RedeemableItem,
  RedeemableItemRepository,
  RedeemableItemType,
  RedeemableItemTypeRepository,
  UserPointHistoryRepository,
  UserPointRepository,
  UserRedeemableItemRepository,
} from '../../../entities/index.js';
import type { RedeemableItemTypesDto } from '../dto/index.js';

@Injectable()
export class RedeemableItemService {
  constructor(
    private readonly redeemableItemTypeRepository: RedeemableItemTypeRepository,
    private readonly redeemableItemRepository: RedeemableItemRepository,
    private readonly userPointRepository: UserPointRepository,
    private readonly userRedeemableItemRepository: UserRedeemableItemRepository,
    private readonly userPointHistoryRepository: UserPointHistoryRepository,
    private em: EntityManager,
  ) {}

  public async getRedeemableItems(): Promise<RedeemableItemTypesDto[]> {
    const results = await this.redeemableItemTypeRepository.findAll({
      populate: ['items'],
      orderBy: { items: { point: 'asc' } },
    });

    return results.map((item: RedeemableItemType) => ({
      id: item.id,
      type: item.type,
      name: item.name,
      items: item.items.map((item: RedeemableItem) => ({
        id: item.id,
        name: item.name,
        description: item.description,
        thumbnail: item.thumbnail,
        point: item.point,
        value: item.value,
      })),
    }));
  }

  @Transactional()
  public async claimItem(userId: number, itemId: number): Promise<void> {
    const redeemableItem = await this.redeemableItemRepository.findOne({ id: itemId });
    if (!redeemableItem) {
      throw new BadRequestException({
        code: RedeemableItemErrorCode.REDEEMABLE_ITEM_NOT_FOUND,
        message: 'Mã quà tặng không tồn tại',
      });
    }

    const userPoint = await this.userPointRepository.findOne({ userId });
    if (!userPoint || userPoint.points < redeemableItem.point) {
      throw new BadRequestException({
        code: RedeemableItemErrorCode.USER_POINT_NOT_ENOUGH,
        message: 'Số điểm không đủ để đổi quà',
      });
    }

    // luu thong tin voucher
    const userItem = this.userRedeemableItemRepository.create({
      userId,
      redeemableItem: redeemableItem,
      code: this.generateRandomCode(),
    });
    this.em.persist(userItem);
    // luu lich su doi diem
    const history = this.userPointHistoryRepository.create({
      userId,
      itemName: redeemableItem.name,
      points: redeemableItem.point * -1,
      redeemableItemId: redeemableItem.id,
    });
    this.em.persist(history);

    userPoint.points -= redeemableItem.point;
    await this.em.flush();
  }

  private generateRandomCode(): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';

    // Đảm bảo có ít nhất 1 ký tự chữ và 1 ký tự số
    const randomChar = characters[Math.floor(Math.random() * characters.length)];
    const randomNumber = numbers[Math.floor(Math.random() * numbers.length)];

    // Kết hợp các ký tự còn lại
    const allCharacters = characters + numbers;
    let result = randomChar + randomNumber;
    for (let i = 0; i < 6; i++) {
      const randomIndex = Math.floor(Math.random() * allCharacters.length);
      result += allCharacters[randomIndex];
    }

    // Trộn ngẫu nhiên các ký tự
    return result
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }
}

import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module } from '@nestjs/common';

import { RedeemableItemController } from './controllers/redeemable-item.controller.js';
import { RedeemableItemService } from './services/index.js';
import { RedeemableItem, RedeemableItemType, UserPoint, UserPointHistory, UserRedeemableItem } from '../../entities/index.js';

@Module({
  imports: [MikroOrmModule.forFeature([RedeemableItem, RedeemableItemType, UserRedeemableItem, UserPoint, UserPointHistory])],
  controllers: [RedeemableItemController],
  providers: [RedeemableItemService],
})
export class RedeemableItemModule {}

import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { GetUser } from '../../../common/decorators/index.js';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard.js';
import type { User } from '../../../common/interfaces/index.js';
import type { RedeemableItemTypesDto } from '../dto/index.js';
import { RedeemableItemService } from '../services/index.js';

@Controller('redeemable-items')
@ApiTags('Redeemable Items')
@UseGuards(JwtAuthGuard)
export class RedeemableItemController {
  constructor(private readonly redeemableItemService: RedeemableItemService) {}

  @Get('')
  public getRedeemableItems(): Promise<RedeemableItemTypesDto[]> {
    return this.redeemableItemService.getRedeemableItems();
  }

  @Post('claim')
  public async claimItem(@GetUser() user: User, @Body('itemId') itemId: number): Promise<void> {
    await this.redeemableItemService.claimItem(user.id, itemId);
  }
}

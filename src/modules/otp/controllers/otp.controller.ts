import { Body, Controller, Post } from '@nestjs/common';

import { OtpActionEnum } from '../../../common/enums/otp-action.enum.js';
import type { SendOtpDto, VerifyOtpDto } from '../dto/index.js';
import { OtpService } from '../services/index.js';

@Controller('otp')
export class OtpController {
  // private readonly logger: Logger = new Logger(AuthController.name);

  constructor(private readonly otpService: OtpService) {}

  @Post('send')
  public async send(@Body() dto: SendOtpDto): Promise<string> {
    await this.otpService.send(dto);
    return 'Chúng tôi đã gửi mã xác thực đến số điện thoại của bạn. Vui lòng kiểm tra tin nhắn!';
  }

  @Post('verify')
  public async verify(@Body() dto: VerifyOtpDto): Promise<{ accessToken: string } | { resetToken: string } | string> {
    const result = await this.otpService.verify(dto);

    if (dto.action === OtpActionEnum.PHONE_VERIFICATION && result.accessToken) {
      return {
        accessToken: result.accessToken,
      };
    }
    return 'Xác thực thành công!';
  }
}

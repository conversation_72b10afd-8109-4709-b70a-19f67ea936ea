import { Transform } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';

import type { OtpActionEnum } from '../../../common/enums/index.js';

export class VerifyOtpDto {
  @IsNotEmpty()
  readonly action!: OtpActionEnum;

  @IsNotEmpty()
  @Transform(({ value }) => value.replace(/\s+/g, ''))
  readonly phone!: string;

  @IsNotEmpty()
  @IsString()
  readonly otp!: string;
}

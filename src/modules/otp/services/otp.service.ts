import { Injectable, Logger } from '@nestjs/common';

import { OtpActionEnum, OtpErrorCode } from '../../../common/enums/index.js';
import { BadRequestException } from '../../../common/exceptions/index.js';
import { SSOClientService, type SSOAuthResponse } from '../../../common/services/sso-client.service.js';
import type { SendOtpDto, VerifyOtpDto } from '../dto/index.js';

@Injectable()
export class OtpService {
  private readonly logger = new Logger(OtpService.name);

  constructor(private readonly ssoClient: SSOClientService) {}

  public async send(dto: SendOtpDto): Promise<void> {
    const ssoOtpType = this.mapOtpActionToSSOType(dto.action);

    await this.ssoClient.sendOtp({
      phone: dto.phone,
      type: ssoOtpType,
    });
  }

  public async verify(dto: VerifyOtpDto): Promise<SSOAuthResponse> {
    const ssoOtpType = this.mapOtpActionToSSOType(dto.action);

    try {
      const result = await this.ssoClient.verifyOtp({
        phone: dto.phone,
        otp: dto.otp,
        type: ssoOtpType,
      });

      return result;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error('OTP verification failed:', error);
      throw new BadRequestException({
        code: OtpErrorCode.OTP_NOT_VALID_OR_EXPIRED,
        message: 'Bạn đã nhập sai mã xác thực hoặc mã xác thực đã hết hạn, vui lòng thử lại!',
      });
    }
  }

  private mapOtpActionToSSOType(action: OtpActionEnum): 'phone_verification' | 'forgot_password' | 'change_password' | 'delete_account' {
    switch (action) {
      case OtpActionEnum.PHONE_VERIFICATION:
        return 'phone_verification';
      case OtpActionEnum.FORGOT_PASSWORD:
        return 'forgot_password';
      case OtpActionEnum.CHANGE_PASSWORD:
        return 'change_password';
      case OtpActionEnum.DELETE_ACCOUNT:
        return 'delete_account';
      default:
        return 'phone_verification';
    }
  }
}

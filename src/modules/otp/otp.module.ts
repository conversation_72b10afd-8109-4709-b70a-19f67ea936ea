import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module } from '@nestjs/common';

import { OtpController } from './controllers/otp.controller.js';
import { OtpService } from './services/index.js';
import { SSOModule } from '../../common/sso/sso.module.js';
import { User, UserOtp } from '../../entities/index.js';

@Module({
  imports: [MikroOrmModule.forFeature([User, UserOtp]), SSOModule],
  controllers: [OtpController],
  providers: [OtpService],
  exports: [OtpService],
})
export class OtpModule {}

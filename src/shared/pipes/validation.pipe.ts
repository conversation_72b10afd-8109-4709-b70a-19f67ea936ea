import type { Dictionary } from '@mikro-orm/mysql';
import { type ArgumentMetadata, BadRequestException, HttpException, HttpStatus, Injectable, type PipeTransform } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validate, ValidationError } from 'class-validator';

@Injectable()
export class ValidationPipe implements PipeTransform {
  async transform(value: unknown, metadata: ArgumentMetadata) {
    if (!value) {
      throw new BadRequestException('Không có dữ liệu đầu vào');
    }

    const { metatype } = metadata;
    // eslint-disable-next-line no-console
    console.log('ValidationPipe', !metatype, !this.toValidate(metatype));

    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }
    const result: object = plainToInstance(metatype, value);
    // eslint-disable-next-line no-console
    console.log('ValidationPipe', result);

    const errors = await validate(result);

    // eslint-disable-next-line no-console
    console.log('ValidationPipe', errors);
    if (errors.length > 0) {
      throw new HttpException({ message: 'Xác thực dữ liệu đầu vào thất bại', errors: this.buildError(errors) }, HttpStatus.BAD_REQUEST);
    }
    return value;
  }

  private buildError(errors: ValidationError[]) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const result = {} as Dictionary;

    for (const el of errors) {
      const prop = el.property;
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      Object.entries(el.constraints!).forEach((constraint) => {
        result[prop + constraint[0]] = constraint[1];
      });
    }

    return result;
  }

  private toValidate(metatype: unknown): boolean {
    const types = [String, Boolean, Number, Array, Object];
    return !types.find((type) => metatype === type);
  }
}

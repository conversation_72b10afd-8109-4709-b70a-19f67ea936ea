import { MySqlDriver } from '@mikro-orm/mysql';
import type { MikroOrmModuleOptions } from '@mikro-orm/nestjs';

export const config = {
  mikro: {
    driver: MySqlDriver,
    // entities: [`${import.meta.dirname}/../../entities`],
    // entitiesTs: [`${import.meta.dirname}/../../entities`],
    autoLoadEntities: true,
    // dbName: 'dst_mall_db',
    // timezone: '+09:00',
    allowGlobalContext: true,
  } satisfies MikroOrmModuleOptions<MySqlDriver>,

  jwtSecret: process.env.JWT_SECRET,
};

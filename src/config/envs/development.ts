import { MySqlDriver } from '@mikro-orm/mysql';
import type { MikroOrmModuleOptions } from '@mikro-orm/nestjs';

export const config = {
  env: 'development',

  mikro: {
    debug: true,
    host: process.env['DB_HOST'] ?? '127.0.0.1',
    user: process.env['DB_USER'],
    password: process.env['DB_PASSWORD'],
    dbName: process.env['DB_NAME'],
    pool: {
      min: 0,
      max: 5,
      idleTimeoutMillis: 10000,
      acquireTimeoutMillis: 10000,
      destroyTimeoutMillis: 60000,
    },
  } satisfies MikroOrmModuleOptions<MySqlDriver>,

  awsRegion: process.env['AWS_REGION'],
  awsAccessKeyId: process.env['AWS_ACCESS_KEY_ID'],
  awsSecretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'],
  s3UploadBucket: process.env['S3_UPLOAD_BUCKET'],

  drceuticsApiUrl: process.env['DRCEUTICS_API_URL'],
  drceuticsApiKey: process.env['DRCEUTICS_API_KEY'],

  vendureApiUrl: process.env['VENDURE_API_URL'],
  vendureChannelToken: process.env['VENDURE_CHANNEL_TOKEN'],

  esmsApiKey: process.env['ESMS_API_KEY'],
  esmsSecretKey: process.env['ESMS_SECRET_KEY'],

  ssoBaseUrl: process.env['SSO_BASE_URL'],
  serviceAud: process.env['SERVICE_AUD'],
  ssoJwtPublicKeysJson: process.env['SSO_JWT_PUBLIC_KEYS_JSON'],
};

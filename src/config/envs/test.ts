import type { MySqlDriver } from '@mikro-orm/mysql';
import type { MikroOrmModuleOptions } from '@mikro-orm/nestjs';

export const config = {
  env: 'test',

  mikro: {
    debug: true,
    host: process.env['DB_HOST'] ?? '127.0.0.1',
    user: process.env['DB_USER'],
    password: process.env['DB_PASSWORD'],
    dbName: process.env['DB_NAME'],
  } satisfies MikroOrmModuleOptions<MySqlDriver>,

  awsRegion: process.env['AWS_REGION'],
  awsAccessKeyId: process.env['AWS_ACCESS_KEY_ID'],
  awsSecretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'],
  s3UploadBucket: process.env['S3_UPLOAD_BUCKET'],

  drceuticsApiUrl: process.env['DRCEUTICS_API_URL'],
  drceuticsApiKey: process.env['DRCEUTICS_API_KEY'],
};

/* eslint-disable @typescript-eslint/no-explicit-any */
import { type ArgumentsHost, Catch, type ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';

import { DstMallException } from './exceptions/index.js';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    // Lấy mã trạng thái
    const status: HttpStatus = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
    const errorCode: number = exception instanceof DstMallException ? exception.code : 400;

    if (status === HttpStatus.INTERNAL_SERVER_ERROR) {
      // Notifications
      // push notification to admin
    }

    // Chuẩn hóa cấu trúc lỗi
    const errorResponse = {
      requestId: request.id, // Nếu bạn có một ID yêu cầu
      path: request.url,
      statusCode: status,
      errorCode,
      timestamp: new Date().toISOString(),
      message: exception.message ?? 'Đã có lỗi xảy ra',
      details: exception instanceof HttpException ? undefined : exception,
    };

    // Sử dụng phương thức Fastify để trả về phản hồi
    response.status(HttpStatus.OK).send(errorResponse); // Fastify sử dụng `send` để trả về JSON
  }
}

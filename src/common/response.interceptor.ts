/* eslint-disable @typescript-eslint/no-explicit-any */
import { type <PERSON><PERSON><PERSON><PERSON>, type ExecutionContext, Injectable, type NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Chỉ áp dụng cho HTTP hoặc HTTPS requests
    if (context.getType() !== 'http') {
      return next.handle();
    }
    return next.handle().pipe(
      map((data) => ({
        statusCode: context.switchToHttp().getResponse().statusCode,
        timestamp: new Date().toISOString(),
        message: 'Thành công',
        data,
      })),
    );
  }
}

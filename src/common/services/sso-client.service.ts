import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { AxiosError } from 'axios';
import { firstValueFrom, catchError } from 'rxjs';

import { CommonErrorCode } from '../enums/index.js';
import { BadRequestException } from '../exceptions/index.js';

export interface SSOSendOtpDto {
  phone: string;
  type: 'phone_verification' | 'forgot_password' | 'change_password' | 'delete_account';
}

export interface SSORegisterDto {
  phone?: string;
  email?: string;
  password: string;
  name?: string;
}

export interface SSOLoginDto {
  phone?: string;
  email?: string;
  password: string;
}

export interface SSOForgotPasswordDto {
  phone: string;
}

export interface SSOVerifyOtpDto {
  phone: string;
  otp: string;
  type: 'phone_verification' | 'forgot_password' | 'change_password' | 'delete_account';
}

export interface SSOResetPasswordDto {
  resetToken: string;
  newPassword: string;
}

export interface SSOUser {
  id: string;
  email?: string;
  phone?: string;
  name?: string;
  isPhoneVerified: boolean;
  birthDate?: Date;
  address?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SSOApiResponse<T = unknown> {
  errorCode: number;
  message: string;
  data: T;
  timestamp: string;
}

export interface SSOAuthTokens {
  accessToken: string;
  refreshToken: string;
  userId: string;
}

export interface SSOOtpResult {
  message: string;
  otp: string;
}

export interface SSOForgotPasswordResult {
  message: string;
  otp: string;
}

export interface SSOOtpVerificationResult {
  message?: string;
  resetToken?: string;
  accessToken?: string;
}

export interface SSOAuthResponse {
  accessToken?: string;
  refreshToken?: string;
  otp?: string;
  message?: string;
  userId?: string;
  resetToken?: string;
}

interface ErrorData {
  message?: string;
}

@Injectable()
export class SSOClientService {
  private readonly ssoBaseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.ssoBaseUrl = this.configService.getOrThrow<string>('ssoBaseUrl');
  }

  async register(userData: SSORegisterDto): Promise<SSOAuthResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<SSOApiResponse<SSOAuthTokens | SSOOtpResult>>(`${this.ssoBaseUrl}/auth/register`, userData).pipe(
          catchError((error: AxiosError) => {
            const errorData = <ErrorData>error.response?.data;
            throw new BadRequestException({
              code: CommonErrorCode.OPS_ERROR,
              message: errorData.message ?? 'Đăng ký thất bại, vui lòng thử lại!',
            });
          }),
        ),
      );

      const ssoApiResponse = response.data;
      if (ssoApiResponse.errorCode !== 0) {
        throw new BadRequestException({
          code: CommonErrorCode.OPS_ERROR,
          message: ssoApiResponse.message,
        });
      }

      const responseData = ssoApiResponse.data;

      if ('accessToken' in responseData) {
        return {
          accessToken: responseData.accessToken,
          refreshToken: responseData.refreshToken,
          userId: responseData.userId,
          message: 'Đăng ký thành công',
        };
      } else {
        return {
          message: responseData.message,
          otp: responseData.otp,
        };
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Lỗi hệ thống, vui lòng thử lại sau!',
      });
    }
  }

  async login(credentials: SSOLoginDto): Promise<SSOAuthResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService
          .post<SSOApiResponse<SSOAuthTokens>>(`${this.ssoBaseUrl}/auth/login`, credentials, {
            headers: {
              'x-audience': this.configService.get<string>('serviceAud'),
            },
          })
          .pipe(
            catchError((error: AxiosError) => {
              const errorData = <ErrorData>error.response?.data;
              throw new BadRequestException({
                code: CommonErrorCode.OPS_ERROR,
                message: errorData.message ?? 'Đăng nhập thất bại, vui lòng kiểm tra lại thông tin!',
              });
            }),
          ),
      );

      const ssoApiResponse = response.data;
      if (ssoApiResponse.errorCode !== 0) {
        throw new BadRequestException({
          code: CommonErrorCode.OPS_ERROR,
          message: ssoApiResponse.message,
        });
      }

      return {
        accessToken: ssoApiResponse.data.accessToken,
        refreshToken: ssoApiResponse.data.refreshToken,
        userId: ssoApiResponse.data.userId,
        message: 'Đăng nhập thành công',
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Lỗi hệ thống, vui lòng thử lại sau!',
      });
    }
  }

  async forgotPassword(userData: SSOForgotPasswordDto): Promise<SSOAuthResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<SSOApiResponse<SSOForgotPasswordResult>>(`${this.ssoBaseUrl}/auth/forgot-password`, userData).pipe(
          catchError((error: AxiosError) => {
            const errorData = <ErrorData>error.response?.data;
            throw new BadRequestException({
              code: CommonErrorCode.OPS_ERROR,
              message: errorData.message ?? 'Yêu cầu đặt lại mật khẩu thất bại, vui lòng thử lại!',
            });
          }),
        ),
      );

      const ssoApiResponse = response.data;
      if (ssoApiResponse.errorCode !== 0) {
        throw new BadRequestException({
          code: CommonErrorCode.OPS_ERROR,
          message: ssoApiResponse.message,
        });
      }

      return {
        otp: ssoApiResponse.data.otp,
        message: ssoApiResponse.data.message,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Lỗi hệ thống, vui lòng thử lại sau!',
      });
    }
  }

  async verifyOtp(otpData: SSOVerifyOtpDto): Promise<SSOAuthResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService
          .post<SSOApiResponse<SSOOtpVerificationResult>>(
            `${this.ssoBaseUrl}/otp/verify`,
            {
              phone: otpData.phone,
              otp: otpData.otp,
              type: otpData.type,
            },
            {
              headers: {
                'x-audience': this.configService.get<string>('serviceAud'),
              },
            },
          )
          .pipe(
            catchError((error: AxiosError) => {
              const errorData = <ErrorData>error.response?.data;
              throw new BadRequestException({
                code: CommonErrorCode.OPS_ERROR,
                message: errorData.message ?? 'Xác thực OTP thất bại, vui lòng thử lại!',
              });
            }),
          ),
      );

      const ssoApiResponse = response.data;
      if (ssoApiResponse.errorCode !== 0) {
        throw new BadRequestException({
          code: CommonErrorCode.OPS_ERROR,
          message: ssoApiResponse.message,
        });
      }

      return {
        message: ssoApiResponse.data.message,
        resetToken: ssoApiResponse.data.resetToken,
        accessToken: ssoApiResponse.data.accessToken,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Lỗi hệ thống, vui lòng thử lại sau!',
      });
    }
  }

  async sendOtp(otpData: SSOSendOtpDto): Promise<SSOAuthResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService
          .post<SSOApiResponse<SSOOtpResult>>(`${this.ssoBaseUrl}/otp/send`, {
            phone: otpData.phone,
            type: otpData.type,
          })
          .pipe(
            catchError((error: AxiosError) => {
              const errorData = <ErrorData>error.response?.data;
              throw new BadRequestException({
                code: CommonErrorCode.OPS_ERROR,
                message: errorData.message ?? 'Gửi OTP thất bại, vui lòng thử lại!',
              });
            }),
          ),
      );

      const ssoApiResponse = response.data;
      if (ssoApiResponse.errorCode !== 0) {
        throw new BadRequestException({
          code: CommonErrorCode.OPS_ERROR,
          message: ssoApiResponse.message,
        });
      }

      return {
        message: ssoApiResponse.data.message,
        otp: ssoApiResponse.data.otp,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Lỗi hệ thống, vui lòng thử lại sau!',
      });
    }
  }

  async resetPassword(resetData: SSOResetPasswordDto): Promise<SSOAuthResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<SSOApiResponse<{ message: string }>>(`${this.ssoBaseUrl}/auth/reset-password`, resetData).pipe(
          catchError((error: AxiosError) => {
            const errorData = <ErrorData>error.response?.data;
            throw new BadRequestException({
              code: CommonErrorCode.OPS_ERROR,
              message: errorData.message ?? 'Đặt lại mật khẩu thất bại, vui lòng thử lại!',
            });
          }),
        ),
      );

      const ssoApiResponse = response.data;
      if (ssoApiResponse.errorCode !== 0) {
        throw new BadRequestException({
          code: CommonErrorCode.OPS_ERROR,
          message: ssoApiResponse.message,
        });
      }

      return {
        message: ssoApiResponse.data.message,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException({
        code: CommonErrorCode.OPS_ERROR,
        message: 'Lỗi hệ thống, vui lòng thử lại sau!',
      });
    }
  }
}

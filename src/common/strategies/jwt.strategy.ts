import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import type { Request } from 'express';
import jwt from 'jsonwebtoken';
import { ExtractJwt, Strategy } from 'passport-jwt';
import type { StrategyOptions } from 'passport-jwt';

import { AuthService } from '../../modules/auth/services/index.js';
import { UserErrorCode, UserStatusEnum } from '../enums/index.js';
import { BadRequestException } from '../exceptions/index.js';
import type { JwtPayload, User } from '../interfaces/index.js';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly authService: AuthService,
    configService: ConfigService,
  ) {
    const audience = configService.get<string>('serviceAud');

    const publicKeysJson = configService.get<string>('ssoJwtPublicKeysJson');
    const publicKeys: Record<string, string> = publicKeysJson ? JSON.parse(publicKeysJson) : {};

    const options: StrategyOptions = {
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      algorithms: ['RS256', 'HS256'],
      secretOrKeyProvider: (_req: Request, rawToken: string, done: (err: Error | null, secretOrPublicKey: string | Buffer) => void) => {
        try {
          const decoded = <{ header?: { alg?: string; kid?: string } } | null>jwt.decode(rawToken, { complete: true });
          const alg = decoded?.header?.alg;
          const kid = decoded?.header?.kid;

          if (alg === 'RS256') {
            if (!kid) {
              done(new Error('Missing kid'), Buffer.from(''));
              return;
            }
            const key = publicKeys[kid];
            if (!key) {
              done(new Error('Unknown kid'), Buffer.from(''));
              return;
            }
            done(null, key);
            return;
          }

          if (alg === 'HS256') {
            const secret = configService.get<string>('JWT_SECRET');
            if (!secret) {
              done(new Error('HS256 secret not configured'), Buffer.from(''));
              return;
            }
            done(null, secret);
            return;
          }

          done(new Error('Unsupported JWT alg'), Buffer.from(''));
          return;
        } catch (e) {
          done(<Error>e, Buffer.from(''));
          return;
        }
      },
      audience,
    };

    super(options);
  }

  public async validate(payload: JwtPayload): Promise<User | null> {
    const user = await this.authService.me(payload.sub);

    if (!user) {
      return null;
    }

    if (user.deleteFlag) {
      throw new BadRequestException({
        code: UserErrorCode.USER_IS_DELETED,
        message: 'Phiên đăng nhập đã hết hạn',
      });
    }

    if (user.status === UserStatusEnum.INACTIVE) {
      throw new BadRequestException({
        code: UserErrorCode.USER_NOT_ACTIVE,
        message: 'Tài khoản đã bị vô hiệu hoá, liên hệ quản trị viên để biết thêm chi tiết',
      });
    }

    return user;
  }
}

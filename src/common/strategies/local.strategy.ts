import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';

import { AuthService } from '../../modules/auth/services/index.js';
import type { User } from '../interfaces/index.js';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private auth: AuthService) {
    super({ usernameField: 'phone' });
  }

  public async validate(phone: string, password: string): Promise<User> {
    const result = await this.auth.validateUserLogin({ phone, password });

    (<User & { ssoAccessToken?: string }>result.user).ssoAccessToken = result.accessToken;

    return result.user;
  }
}

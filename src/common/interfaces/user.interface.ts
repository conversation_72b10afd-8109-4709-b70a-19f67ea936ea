import type { BooleanEnum, UserRoleEnum, UserStatusEnum } from '../../common/enums/index.js';

export interface User {
  id: number;
  phone: string;
  email?: string;
  fullName?: string;
  avatar?: string;
  address?: string;
  dob?: string;
  gender?: string;
  phoneVerified?: BooleanEnum;
  emailVerified?: BooleanEnum;
  role?: UserRoleEnum;
  deleteFlag?: BooleanEnum;
  status?: UserStatusEnum;
}

import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class S3Service {
  private readonly client: S3Client;
  private readonly s3UploadBucket: string;

  constructor(private readonly configService: ConfigService) {
    const accessKeyId = this.configService.get('awsAccessKeyId');
    const secretAccessKey = this.configService.get('awsSecretAccessKey');
    const region = this.configService.get('awsRegion') ?? 'us-east-1';
    if (!accessKeyId || !secretAccessKey) {
      throw new Error('AWS credentials are not set in the environment variables');
    }

    const s3UploadBucket = this.configService.get('s3UploadBucket');
    if (!s3UploadBucket) {
      throw new Error('S3 upload bucket is not set in the environment variables');
    }
    this.s3UploadBucket = s3UploadBucket;

    const s3Client = new S3Client({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });

    this.client = s3Client;
  }

  public createPutObjectPresignedUrl(key: string, contentType: string): Promise<string> {
    const params = {
      Bucket: this.s3UploadBucket,
      Key: key,
      ContentType: contentType,
    };
    const command = new PutObjectCommand(params);

    return getSignedUrl(this.client, command, {
      expiresIn: 300,
    });
  }
}

/* eslint-disable */
export default async () => {
    const t = {
        ["./common/enums/user.enum.js"]: await import("./common/enums/user.enum.js"),
        ["./entities/user.entity.js"]: await import("./entities/user.entity.js"),
        ["./entities/user-otp.entity.js"]: await import("./entities/user-otp.entity.js"),
        ["./common/enums/otp-action.enum.js"]: await import("./common/enums/otp-action.enum.js"),
        ["./common/enums/boolean.enum.js"]: await import("./common/enums/boolean.enum.js"),
        ["./entities/product-point.entity.js"]: await import("./entities/product-point.entity.js"),
        ["./entities/product-qr-code.entity.js"]: await import("./entities/product-qr-code.entity.js"),
        ["./entities/user-point.entity.js"]: await import("./entities/user-point.entity.js"),
        ["./entities/redeemable-item.entity.js"]: await import("./entities/redeemable-item.entity.js"),
        ["./entities/redeemable-item-type.entity.js"]: await import("./entities/redeemable-item-type.entity.js"),
        ["./entities/user-point-history.entity.js"]: await import("./entities/user-point-history.entity.js"),
        ["./entities/user-redeemable-item.entity.js"]: await import("./entities/user-redeemable-item.entity.js"),
        ["./entities/redeemable-gift.entity.js"]: await import("./entities/redeemable-gift.entity.js"),
        ["./entities/redeemable-product.entity.js"]: await import("./entities/redeemable-product.entity.js"),
        ["./entities/registration-application.entity.js"]: await import("./entities/registration-application.entity.js"),
        ["./modules/redeemable-item/dto/redeemable-items.dto.js"]: await import("./modules/redeemable-item/dto/redeemable-items.dto.js"),
        ["./modules/vendure-redemption/dto/vendure-product.dto.js"]: await import("./modules/vendure-redemption/dto/vendure-product.dto.js"),
        ["./modules/vendure-redemption/dto/redemption.dto.js"]: await import("./modules/vendure-redemption/dto/redemption.dto.js"),
        ["./modules/point-management/dto/qr-info.dto.js"]: await import("./modules/point-management/dto/qr-info.dto.js"),
        ["./modules/user/dto/my-redeemable-items.dto.js"]: await import("./modules/user/dto/my-redeemable-items.dto.js"),
        ["./modules/vendure-redemption/dto/create-order-response.dto.js"]: await import("./modules/vendure-redemption/dto/create-order-response.dto.js")
    };
    return { "@nestjs/swagger": { "models": [[import("./modules/auth/dto/create-user.dto.js"), { "CreateUserDto": { phone: { required: true, type: () => String }, password: { required: true, type: () => String }, type: { required: false, enum: t["./common/enums/user.enum.js"].UserTypeEnum }, email: { required: false, type: () => String, format: "email" }, agentName: { required: false, type: () => String }, representativeName: { required: false, type: () => String }, citizenIdentifyNumber: { required: false, type: () => String }, taxCode: { required: false, type: () => String }, businessLicense: { required: false, type: () => String }, kolEmail: { required: false, type: () => String, format: "email" }, kolRepresentativeName: { required: false, type: () => String }, socialAccountLinks: { required: false, type: () => [String], maxItems: 5 }, kolPhoneNumber: { required: false, type: () => String } } }], [import("./modules/auth/dto/user-login.dto.js"), { "UserLoginDto": { phone: { required: true, type: () => String }, password: { required: true, type: () => String } } }], [import("./modules/auth/dto/forgot-password.dto.js"), { "ForgotPasswordDto": { phone: { required: true, type: () => String } } }], [import("./modules/auth/dto/change-password.dto.js"), { "ChangePasswordDto": { phone: { required: true, type: () => String }, otp: { required: true, type: () => String }, password: { required: true, type: () => String } } }], [import("./entities/base.entity.js"), { "BaseEntity": { id: { required: true, type: () => Number }, createdAt: { required: true, type: () => Object }, updatedAt: { required: true, type: () => Object } } }], [import("./entities/user.entity.js"), { "User": { email: { required: false, type: () => String }, phone: { required: true, type: () => String }, password: { required: false, type: () => String }, fullName: { required: false, type: () => String }, avatar: { required: false, type: () => String }, address: { required: false, type: () => String }, dob: { required: false, type: () => String }, gender: { required: false, type: () => String }, status: { required: false, enum: t["./common/enums/user.enum.js"].UserStatusEnum }, phoneVerified: { required: false, type: () => Number }, emailVerified: { required: false, type: () => Number }, role: { required: false, enum: t["./common/enums/user.enum.js"].UserRoleEnum }, type: { required: false, enum: t["./common/enums/user.enum.js"].UserTypeEnum }, deleteFlag: { required: false, type: () => Number }, deletedAt: { required: false, type: () => Date }, ssoUserId: { required: false, type: () => String } }, "UserRepository": {} }], [import("./entities/user-otp.entity.js"), { "UserOtp": { userId: { required: true, type: () => Number }, action: { required: true, enum: t["./common/enums/otp-action.enum.js"].OtpActionEnum }, otp: { required: true, type: () => String }, isActive: { required: false, enum: t["./common/enums/boolean.enum.js"].BooleanEnum }, expiresAt: { required: true, type: () => Date }, isUsed: { required: false, enum: t["./common/enums/boolean.enum.js"].BooleanEnum } }, "UserOtpRepository": {} }], [import("./entities/product-point.entity.js"), { "ProductPoint": { sku: { required: true, type: () => String }, name: { required: true, type: () => String }, price: { required: true, type: () => Number }, point: { required: false, type: () => Number }, updatedBy: { required: false, type: () => t["./entities/user.entity.js"].User }, updatedAt: { required: true, type: () => Object } }, "ProductPointRepository": {} }], [import("./entities/product-qr-code.entity.js"), { "ProductQrCode": { qrCode: { required: true, type: () => String }, scratchCode: { required: true, type: () => String }, sku: { required: false, type: () => String }, name: { required: false, type: () => String }, price: { required: false, type: () => Number }, manufacturer: { required: false, type: () => String }, dispatchDate: { required: false, type: () => String }, manufactureDate: { required: false, type: () => Date }, point: { required: false, type: () => Number }, isUsed: { required: false, enum: t["./common/enums/boolean.enum.js"].BooleanEnum }, usedAt: { required: false, type: () => Date }, batch: { required: false, type: () => String }, expiresAt: { required: false, type: () => String }, customer: { required: false, type: () => t["./entities/user.entity.js"].User } }, "ProductQrCodeRepository": {} }], [import("./entities/user-point.entity.js"), { "UserPoint": { id: { required: true, type: () => Number }, userId: { required: true, type: () => Number }, points: { required: true, type: () => Number }, updatedAt: { required: true, type: () => Object } }, "UserPointRepository": {} }], [import("./entities/redeemable-item.entity.js"), { "RedeemableItem": { type: { required: true, type: () => t["./entities/redeemable-item-type.entity.js"].RedeemableItemType }, name: { required: true, type: () => String }, description: { required: true, type: () => String }, thumbnail: { required: true, type: () => String }, point: { required: true, type: () => Number }, value: { required: true, type: () => Number } }, "RedeemableItemRepository": {} }], [import("./entities/redeemable-item-type.entity.js"), { "RedeemableItemType": { type: { required: true, type: () => String }, name: { required: true, type: () => String }, items: { required: true, type: () => Object } }, "RedeemableItemTypeRepository": {} }], [import("./entities/user-point-history.entity.js"), { "UserPointHistory": { userId: { required: true, type: () => Number }, itemName: { required: true, type: () => String }, points: { required: true, type: () => Number }, redeemableItemId: { required: false, type: () => Number }, productQrCodeId: { required: false, type: () => Number } }, "UserPointHistoryRepository": {} }], [import("./entities/user-redeemable-item.entity.js"), { "UserRedeemableItem": { userId: { required: true, type: () => Number }, redeemableItem: { required: true, type: () => t["./entities/redeemable-item.entity.js"].RedeemableItem }, code: { required: true, type: () => String }, isUsed: { required: false, enum: t["./common/enums/boolean.enum.js"].BooleanEnum } }, "UserRedeemableItemRepository": {} }], [import("./entities/redeemable-gift.entity.js"), { "RedeemableGift": { type: { required: true, type: () => String }, name: { required: true, type: () => String }, description: { required: false, type: () => String }, isActive: { required: true, type: () => Boolean }, products: { required: true, type: () => Object } }, "RedeemableGiftRepository": {} }], [import("./entities/redeemable-product.entity.js"), { "RedeemableVendureProduct": { redeemableGift: { required: false, type: () => t["./entities/redeemable-gift.entity.js"].RedeemableGift }, productId: { required: true, type: () => String }, variantId: { required: true, type: () => String }, variantName: { required: true, type: () => String }, variantPrice: { required: false, type: () => Number }, pointsCost: { required: true, type: () => Number }, imageUrl: { required: false, type: () => String }, sku: { required: false, type: () => String } }, "RedeemableVendureProductRepository": {} }], [import("./entities/registration-application.entity.js"), { "RegistrationApplication": { user: { required: true, type: () => t["./entities/user.entity.js"].User }, type: { required: true, enum: t["./entities/registration-application.entity.js"].RegistrationApplicationTypeEnum }, status: { required: true, enum: t["./entities/registration-application.entity.js"].RegistrationApplicationStatusEnum }, payload: { required: true, type: () => Object }, note: { required: false, type: () => String }, reviewedAt: { required: false, type: () => Date }, reviewedBy: { required: false, type: () => Number } }, "RegistrationApplicationRepository": {} }], [import("./modules/otp/dto/send-otp.dto.js"), { "SendOtpDto": { action: { required: true, enum: t["./common/enums/otp-action.enum.js"].OtpActionEnum }, phone: { required: true, type: () => String } } }], [import("./modules/otp/dto/verify-otp.dto.js"), { "VerifyOtpDto": { action: { required: true, enum: t["./common/enums/otp-action.enum.js"].OtpActionEnum }, phone: { required: true, type: () => String }, otp: { required: true, type: () => String } } }], [import("./modules/registration-application/dto/review-application.dto.js"), { "ReviewApplicationDto": { status: { required: true, type: () => Object }, note: { required: false, type: () => String } } }], [import("./modules/registration-application/dto/get-applications.dto.js"), { "GetApplicationsDto": { status: { required: false, enum: t["./entities/registration-application.entity.js"].RegistrationApplicationStatusEnum }, type: { required: false, enum: t["./entities/registration-application.entity.js"].RegistrationApplicationTypeEnum }, limit: { required: false, type: () => Number, default: 20 }, offset: { required: false, type: () => Number, default: 0 } } }], [import("./modules/point-management/dto/drceutics-reponse.dto.js"), { "DrCeuticsResponseDto": { giaxuat: { required: true, type: () => String }, solo: { required: true, type: () => String }, MaSP: { required: true, type: () => String }, tensp: { required: true, type: () => String }, Tennsx: { required: true, type: () => String }, handung: { required: true, type: () => String }, ngaynhapxuat: { required: true, type: () => String }, ngaysanxuat: { required: false, type: () => String } } }], [import("./modules/point-management/dto/qr-info.dto.js"), { "QrInfoDto": { sku: { required: true, type: () => String }, name: { required: true, type: () => String }, price: { required: true, type: () => Number }, manufacturer: { required: true, type: () => String }, batch: { required: true, type: () => String }, expiresAt: { required: true, type: () => String }, dispatchDate: { required: true, type: () => String }, manufactureDate: { required: false, type: () => Date }, isUsed: { required: false, type: () => Boolean } } }], [import("./modules/point-management/dto/apply-qr.dto.js"), { "ApplyQrDto": { sku: { required: true, type: () => String }, scratchCode: { required: true, type: () => String }, qrCode: { required: true, type: () => String } } }], [import("./modules/point-management/dto/generate-qr.dto.js"), { "GenerateQrDto": { count: { required: false, type: () => Number, default: 210000, minimum: 1, maximum: 500000 } } }], [import("./modules/redeemable-item/dto/redeemable-items.dto.js"), { "RedeemableItemDto": { id: { required: true, type: () => Number }, name: { required: true, type: () => String }, description: { required: true, type: () => String }, thumbnail: { required: true, type: () => String }, point: { required: true, type: () => Number }, value: { required: true, type: () => Number } }, "RedeemableItemTypesDto": { id: { required: true, type: () => Number }, type: { required: true, type: () => String }, name: { required: true, type: () => String }, items: { required: true, type: () => [t["./modules/redeemable-item/dto/redeemable-items.dto.js"].RedeemableItemDto] } } }], [import("./modules/storage/dto/get-presigned.dto.js"), { "GetPresignedDto": { path: { required: true, type: () => String } } }], [import("./modules/user/dto/update-profile.dto.js"), { "UpdateProfileDto": { fullName: { required: true, type: () => String }, avatar: { required: false, type: () => String }, email: { required: true, type: () => String, format: "email" }, dob: { required: true, type: () => String }, gender: { required: true, type: () => String }, address: { required: true, type: () => String } } }], [import("./modules/user/dto/update-password.dto.js"), { "UpdatePasswordDto": { oldPassword: { required: true, type: () => String }, newPassword: { required: true, type: () => String }, otp: { required: true, type: () => String } } }], [import("./modules/user/dto/my-redeemable-items.dto.js"), { "MyRedeemableItemsDto": { id: { required: true, type: () => Number }, itemId: { required: true, type: () => Number }, name: { required: true, type: () => String }, description: { required: true, type: () => String }, thumbnail: { required: true, type: () => String }, point: { required: true, type: () => Number }, value: { required: true, type: () => Number }, code: { required: true, type: () => String } } }], [import("./modules/vendure-redemption/dto/vendure-product.dto.js"), { "VendureProductDto": { id: { required: true, type: () => Number }, productId: { required: true, type: () => String }, variantId: { required: true, type: () => String }, variantName: { required: true, type: () => String }, pointsCost: { required: true, type: () => Number }, variantPrice: { required: false, type: () => Number }, imageUrl: { required: false, type: () => String }, sku: { required: false, type: () => String }, stockLevel: { required: false, type: () => String } }, "VendureProductListDto": { products: { required: true, type: () => [t["./modules/vendure-redemption/dto/vendure-product.dto.js"].VendureProductDto] }, total: { required: true, type: () => Number } } }], [import("./modules/vendure-redemption/dto/redemption.dto.js"), { "ShippingAddressDto": { email: { required: true, type: () => String }, fullName: { required: true, type: () => String }, streetLine1: { required: true, type: () => String }, countryCode: { required: true, type: () => String }, phoneNumber: { required: true, type: () => String }, district: { required: true, type: () => String }, ward: { required: true, type: () => String }, province: { required: true, type: () => String } }, "RedeemProductDto": { productId: { required: true, type: () => Number }, quantity: { required: true, type: () => Number, minimum: 1 }, shippingAddress: { required: true, type: () => t["./modules/vendure-redemption/dto/redemption.dto.js"].ShippingAddressDto } } }], [import("./modules/vendure-redemption/dto/create-order-response.dto.js"), { "CreateOrderResponseDto": { id: { required: true, type: () => String }, code: { required: true, type: () => String }, state: { required: true, type: () => String }, total: { required: true, type: () => Number }, subTotal: { required: true, type: () => Number } } }], [import("./modules/otp/dto/esms-response.dto.js"), { "ESmsResponseDto": { CodeResult: { required: true, type: () => String }, CountRegenerate: { required: true, type: () => String }, SMSID: { required: true, type: () => String } } }]], "controllers": [[import("./modules/auth/controllers/auth.controller.js"), { "AuthController": { "register": { type: String }, "login": {}, "forgotPassword": { type: String }, "changePassword": {} } }], [import("./modules/otp/controllers/otp.controller.js"), { "OtpController": { "send": { type: String }, "verify": { type: Object } } }], [import("./modules/registration-application/controllers/registration-application.controller.js"), { "RegistrationApplicationController": { "getMyApplications": { type: [t["./entities/registration-application.entity.js"].RegistrationApplication] }, "getAllApplications": {}, "getApplicationById": { type: t["./entities/registration-application.entity.js"].RegistrationApplication }, "reviewApplication": { type: t["./entities/registration-application.entity.js"].RegistrationApplication }, "cancelApplication": { type: t["./entities/registration-application.entity.js"].RegistrationApplication } } }], [import("./modules/health/health.controller.js"), { "HealthController": { "check": { type: Object } } }], [import("./modules/point-management/controllers/point-management.controller.js"), { "PointManagementController": { "scanQr": { type: t["./modules/point-management/dto/qr-info.dto.js"].QrInfoDto }, "applyScratchCode": {}, "generateQrCodes": {} } }], [import("./modules/redeemable-item/controllers/redeemable-item.controller.js"), { "RedeemableItemController": { "getRedeemableItems": { type: [t["./modules/redeemable-item/dto/redeemable-items.dto.js"].RedeemableItemTypesDto] }, "claimItem": {} } }], [import("./modules/storage/controllers/storage.controller.js"), { "StorageController": { "register": { type: String } } }], [import("./modules/user/controllers/user.controller.js"), { "UserController": { "login": { type: Object }, "updateProfile": {}, "updatePassword": {}, "pointHistories": {}, "myPoints": {}, "myRedeemableItems": { type: [t["./modules/user/dto/my-redeemable-items.dto.js"].MyRedeemableItemsDto] } } }], [import("./modules/vendure-redemption/controllers/vendure-redemption.controller.js"), { "VendureRedemptionController": { "getProducts": { type: t["./modules/vendure-redemption/dto/vendure-product.dto.js"].VendureProductListDto }, "redeemProduct": { type: t["./modules/vendure-redemption/dto/create-order-response.dto.js"].CreateOrderResponseDto }, "getOrderHistory": { type: Object }, "getProductVariant": { type: Object } } }]] } };
};
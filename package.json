{"name": "dstmall-be", "version": "1.0.0", "description": "DST Mall Backend", "license": "MIT", "private": true, "type": "module", "engines": {"node": ">=20.19.0 || >=22.12.0"}, "scripts": {"lint": "eslint", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "build": "nest build", "start": "node dist/app", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "test": "vitest run --exclude **/e2e/**/*.ts", "test:e2e": "vitest run --config ./test/vitest.e2e.ts", "esm": "node --import @swc-node/register/esm-register", "entity:load": "npm run esm ./bin/entities.ts", "entity:sync": "npm run esm ./bin/synchronize.ts", "doc:api": "nest build && node dist/swagger"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@fastify/compress": "^8.0.1", "@fastify/cookie": "^11.0.2", "@fastify/helmet": "^13.0.1", "@fastify/passport": "^3.0.2", "@fastify/session": "^11.1.0", "@fastify/static": "^8.1.1", "@mikro-orm/core": "^6.4.13", "@mikro-orm/mysql": "^6.4.13", "@mikro-orm/nestjs": "^6.1.1", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.16", "@nestjs/config": "4.0.2", "@nestjs/core": "^11.0.16", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-fastify": "^11.0.16", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.1.2", "@nestjs/terminus": "^11.0.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "nestjs-pino": "^4.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino": "^9.6.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}, "devDependencies": {"@eslint/js": "^9.24.0", "@inquirer/prompts": "^7.4.1", "@mikro-orm/cli": "^6.4.13", "@mikro-orm/entity-generator": "^6.4.13", "@nestjs/cli": "^11.0.6", "@nestjs/testing": "^11.0.16", "@swc-node/register": "^1.10.10", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.20", "@types/bcrypt": "^5.0.2", "@types/node": "^22.14.1", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.3", "@vitest/eslint-plugin": "^1.1.42", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^4.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-sonarjs": "^3.0.2", "prettier": "^3.5.3", "supertest": "^7.1.0", "typescript": "~5.8.3", "typescript-eslint": "^8.29.1", "unplugin-swc": "^1.5.1", "vitest": "^3.1.1", "vitest-mock-extended": "^3.1.0"}}
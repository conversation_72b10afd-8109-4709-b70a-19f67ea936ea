# 1. Sử dụng Node.js làm base image
FROM node:22-alpine AS builder

# 2. Thiế<PERSON> lập thư mục làm việc trong container
WORKDIR /app

# 3. Copy file package.json và package-lock.json (nếu có)
COPY package*.json ./

# 4. Cài đặt dependencies
RUN npm install

# 5. Copy toàn bộ mã nguồn vào container
COPY . .

# 6. Build ứng dụng NestJS
RUN npm run build

# 7. Sử dụng một base image nhỏ gọn hơn để chạy ứng dụng
FROM node:22-alpine

# 8. Thiết lập thư mục làm việc
WORKDIR /app

# 9. Copy file cần thiết từ bước build
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package*.json ./

# 10. Cài đặt chỉ các dependencies cần thiết
RUN npm install --only=production

# 11. Mở cổng ứng dụng
EXPOSE 3000

# 12. Khởi chạy ứng dụng
CMD ["node", "dist/app"]

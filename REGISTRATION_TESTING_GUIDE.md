# Registration Testing Guide

## Sample Registration Payloads

### 1. KOL Registration

**Endpoint**: `POST /auth/register`

**Payload**:
```json
{
  "phone": "**********",
  "password": "SecurePassword123!",
  "type": "kol",
  "kolEmail": "<EMAIL>",
  "kolRepresentativeName": "Nguyen Van A",
  "socialAccountLinks": [
    "https://instagram.com/nguyen_van_a",
    "https://tiktok.com/@nguyen_van_a",
    "https://youtube.com/c/NguyenVanAChannel",
    "https://facebook.com/nguyen.van.a.official"
  ],
  "kolPhoneNumber": "**********"
}
```

**Expected Response**:
```json
"Đăng ký tài khoản thành công. Tài khoản của bạn hiện là loại Retail. Đơn đăng ký KOL sẽ được admin xem xét và phê duyệt để nâng cấp tài k<PERSON>."
```

**cURL Command**:
```bash
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "**********",
    "password": "SecurePassword123!",
    "type": "kol",
    "kolEmail": "<EMAIL>",
    "kolRepresentativeName": "Nguyen Van A",
    "socialAccountLinks": [
      "https://instagram.com/nguyen_van_a",
      "https://tiktok.com/@nguyen_van_a",
      "https://youtube.com/c/NguyenVanAChannel",
      "https://facebook.com/nguyen.van.a.official"
    ],
    "kolPhoneNumber": "**********"
  }'
```

### 2. Wholesale Registration

**Endpoint**: `POST /auth/register`

**Payload**:
```json
{
  "phone": "**********",
  "password": "SecurePassword456!",
  "type": "wholesale",
  "email": "<EMAIL>",
  "agentName": "Tran Thi B",
  "representativeName": "Le Van Manager",
  "citizenIdentifyNumber": "************",
  "taxCode": "**********",
  "businessLicense": "/uploads/business-license-123.pdf"
}
```

**Expected Response**:
```json
"Đăng ký tài khoản thành công. Tài khoản của bạn hiện là loại Retail. Đơn đăng ký Wholesale sẽ được admin xem xét và phê duyệt để nâng cấp tài khoản."
```

**cURL Command**:
```bash
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "**********",
    "password": "SecurePassword456!",
    "type": "wholesale",
    "email": "<EMAIL>",
    "agentName": "Tran Thi B",
    "representativeName": "Le Van Manager",
    "citizenIdentifyNumber": "************",
    "taxCode": "**********",
    "businessLicense": "/uploads/business-license-123.pdf"
  }'
```

### 3. Retail Registration (Explicit)

**Endpoint**: `POST /auth/register`

**Payload**:
```json
{
  "phone": "**********",
  "password": "SimplePassword789!",
  "type": "retail"
}
```

**Expected Response**:
```json
"Đăng ký tài khoản thành công"
```

**cURL Command**:
```bash
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "**********",
    "password": "SimplePassword789!",
    "type": "retail"
  }'
```

### 4. Retail Registration (Default - No Type)

**Endpoint**: `POST /auth/register`

**Payload**:
```json
{
  "phone": "**********",
  "password": "DefaultPassword000!"
}
```

**Expected Response**:
```json
"Đăng ký tài khoản thành công"
```

**cURL Command**:
```bash
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "**********",
    "password": "DefaultPassword000!"
  }'
```

## Registration Application Management

### Get User's Applications

**Endpoint**: `GET /registration-applications/my-applications`
**Headers**: `Authorization: Bearer <jwt_token>`

**cURL Command**:
```bash
curl -X GET http://localhost:3000/registration-applications/my-applications \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Admin: Get All Applications

**Endpoint**: `GET /registration-applications`
**Headers**: `Authorization: Bearer <admin_jwt_token>`
**Query Parameters**:
- `status`: `pending`, `approved`, `rejected`, `cancelled`
- `type`: `kol`, `wholesale`
- `limit`: number (default: 20)
- `offset`: number (default: 0)

**cURL Command**:
```bash
curl -X GET "http://localhost:3000/registration-applications?status=pending&type=kol&limit=10" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

### Admin: Review Application

**Endpoint**: `PUT /registration-applications/:id/review`
**Headers**: `Authorization: Bearer <admin_jwt_token>`

**Approve Application**:
```json
{
  "status": "approved",
  "note": "Application meets all requirements"
}
```

**Reject Application**:
```json
{
  "status": "rejected",
  "note": "Missing required documentation"
}
```

**cURL Command (Approve)**:
```bash
curl -X PUT http://localhost:3000/registration-applications/1/review \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "approved",
    "note": "Application meets all requirements"
  }'
```

**cURL Command (Reject)**:
```bash
curl -X PUT http://localhost:3000/registration-applications/1/review \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "rejected",
    "note": "Missing required documentation"
  }'
```

### User: Cancel Application

**Endpoint**: `PUT /registration-applications/:id/cancel`
**Headers**: `Authorization: Bearer <jwt_token>`

**cURL Command**:
```bash
curl -X PUT http://localhost:3000/registration-applications/1/cancel \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Validation Test Cases

### Invalid KOL Registration (Missing Required Fields)

```json
{
  "phone": "**********",
  "password": "SecurePassword123!",
  "type": "kol"
  // Missing kolEmail and kolRepresentativeName
}
```

**Expected**: Validation error for missing required KOL fields

### Invalid Wholesale Registration (Missing Required Fields)

```json
{
  "phone": "**********",
  "password": "SecurePassword456!",
  "type": "wholesale"
  // Missing email, agentName, representativeName, etc.
}
```

**Expected**: Validation error for missing required wholesale fields

### Invalid Social Links (More than 5)

```json
{
  "phone": "**********",
  "password": "SecurePassword123!",
  "type": "kol",
  "kolEmail": "<EMAIL>",
  "kolRepresentativeName": "Test User",
  "socialAccountLinks": [
    "https://instagram.com/1",
    "https://tiktok.com/2",
    "https://youtube.com/3",
    "https://facebook.com/4",
    "https://twitter.com/5",
    "https://linkedin.com/6"
  ]
}
```

**Expected**: Validation error for exceeding 5 social links

## Testing Checklist

### Registration Flow
- [ ] KOL registration creates user as retail + application
- [ ] Wholesale registration creates user as retail + application
- [ ] Retail registration works normally
- [ ] Registration without type defaults to retail
- [ ] Validation works for all required fields
- [ ] Proper error messages for missing fields

### User Management
- [ ] Users can view their applications
- [ ] Users can cancel pending applications
- [ ] Users cannot access other users' applications

### Admin Management
- [ ] Admin can view all applications
- [ ] Admin can filter applications by status/type
- [ ] Admin can approve applications (user type updated)
- [ ] Admin can reject applications (user type remains retail)
- [ ] Admin can add review notes

### Security
- [ ] Non-admin users cannot review applications
- [ ] Users cannot review their own applications
- [ ] JWT authentication required for protected endpoints

## Expected Database State After Registration

### After KOL Registration:
**Users Table**:
```sql
id: 1, phone: "**********", type: "retail", ...
```

**Registration Applications Table**:
```sql
id: 1, user_id: 1, type: "kol", status: "pending", payload: {...}, ...
```

### After Admin Approval:
**Users Table**:
```sql
id: 1, phone: "**********", type: "kol", ...
```

**Registration Applications Table**:
```sql
id: 1, user_id: 1, type: "kol", status: "approved", reviewedAt: "2024-01-01 10:00:00", ...
```

# Registration Flow Changes - Summary

## Overview
Extended the existing registration flow to support 3 user types: retail, KOL, and wholesale with additional registration forms and admin approval workflow.

**Important**: All users start as `retail` type upon registration. KOL and wholesale users only get upgraded to their requested type after admin approval of their registration application.

## Changes Made

### 1. User Entity Updates
- **File**: `src/entities/user.entity.ts`
- **Changes**: Added `type` field with enum values: `retail`, `kol`, `wholesale`
- **Default**: `retail`

### 2. User Type Enum
- **File**: `src/common/enums/user.enum.ts`
- **Added**: `UserTypeEnum` with values `RETAIL`, `KOL`, `WHOLESALE`

### 3. Registration Application Entity
- **File**: `src/entities/registration-application.entity.ts`
- **Purpose**: Handle approval workflow for KOL and wholesale registrations
- **Fields**:
  - `id`: Primary key
  - `userId`: Reference to user
  - `type`: `kol` or `wholesale`
  - `status`: `pending`, `approved`, `rejected`, `cancelled`
  - `payload`: JSON data containing form fields
  - `note`: Admin review notes
  - `reviewedAt`: Review timestamp
  - `reviewedBy`: Admin user ID

### 4. Refactored CreateUserDto
- **File**: `src/modules/auth/dto/create-user.dto.ts`
- **Changes**: Added conditional validation for KOL and wholesale fields
- **Wholesale Fields**:
  - `email` (required)
  - `agentName` (required)
  - `representativeName` (required)
  - `citizenIdentifyNumber` (required)
  - `taxCode` (required)
  - `businessLicense` (required) - file path/URL
- **KOL Fields**:
  - `kolEmail` (required)
  - `kolRepresentativeName` (required)
  - `socialAccountLinks` (optional, max 5)
  - `kolPhoneNumber` (optional)

### 5. Updated Auth Service
- **File**: `src/modules/auth/services/auth.service.ts`
- **Changes**: 
  - Modified `register()` method to handle all user types
  - Removed separate `extendedRegister()` method
  - Added logic to create registration applications for KOL/wholesale users
  - Updated user sync to handle user types

### 6. Registration Application Module
- **Files**: 
  - `src/modules/registration-application/registration-application.module.ts`
  - `src/modules/registration-application/services/registration-application.service.ts`
  - `src/modules/registration-application/controllers/registration-application.controller.ts`
- **Features**:
  - Create registration applications
  - Admin review (approve/reject)
  - User can view their applications
  - User can cancel pending applications
  - List all applications with filtering (admin only)

## API Endpoints

### Registration
- **POST** `/auth/register`
- **Body**: `CreateUserDto` with optional user type and additional fields
- **Response**: Success message or OTP message

### Registration Application Management
- **GET** `/registration-applications/my-applications` - Get user's applications
- **GET** `/registration-applications` - Get all applications (admin only)
- **GET** `/registration-applications/:id` - Get application by ID
- **PUT** `/registration-applications/:id/review` - Review application (admin only)
- **PUT** `/registration-applications/:id/cancel` - Cancel application

## Registration Flow

### Retail Users (Default)
1. User submits registration with `type: "retail"` or no type
2. System registers with SSO
3. Creates local user with `retail` type
4. Returns success message

### KOL Users
1. User submits registration with `type: "kol"` + additional KOL fields
2. System registers with SSO
3. **Creates local user with `retail` type initially**
4. Creates registration application with KOL payload
5. Returns message about admin review and current retail status
6. Admin reviews and approves/rejects
7. **If approved, user type is updated to `kol`**; if rejected, user remains `retail` and can reapply

### Wholesale Users
1. User submits registration with `type: "wholesale"` + additional wholesale fields
2. System registers with SSO  
3. **Creates local user with `retail` type initially**
4. Creates registration application with wholesale payload
5. Returns message about admin review and current retail status
6. Admin reviews and approves/rejects
7. **If approved, user type is updated to `wholesale`**; if rejected, user remains `retail` and can reapply

## Database Schema Changes

### Users Table
```sql
ALTER TABLE users ADD COLUMN type VARCHAR(20) DEFAULT 'retail';
```

### Registration Applications Table
```sql
CREATE TABLE registration_applications (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  type VARCHAR(20) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  payload JSON NOT NULL,
  note TEXT,
  reviewedAt TIMESTAMP NULL,
  reviewedBy INT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_type_status (type, status),
  INDEX idx_reviewedBy (reviewedBy),
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## Example Payloads

### KOL Registration
```json
{
  "phone": "**********",
  "password": "password123",
  "type": "kol",
  "kolEmail": "<EMAIL>",
  "kolRepresentativeName": "John Doe",
  "socialAccountLinks": [
    "https://instagram.com/johndoe",
    "https://tiktok.com/@johndoe",
    "https://youtube.com/johndoe"
  ],
  "kolPhoneNumber": "**********"
}
```

### Wholesale Registration  
```json
{
  "phone": "**********",
  "password": "password123",
  "type": "wholesale",
  "email": "<EMAIL>",
  "agentName": "Jane Smith",
  "representativeName": "John Manager",
  "citizenIdentifyNumber": "************",
  "taxCode": "*********",
  "businessLicense": "/uploads/license_123.pdf"
}
```

## Testing Checklist

### Registration Testing
- [ ] Retail registration works as before
- [ ] KOL registration creates user as retail + application
- [ ] Wholesale registration creates user as retail + application  
- [ ] Validation works for required fields
- [ ] SSO integration still works
- [ ] Database entities are created correctly
- [ ] Users start as retail type regardless of registration type

### Admin Review Testing
- [ ] Admin can view all applications
- [ ] Admin can approve applications
- [ ] Admin can reject applications  
- [ ] User type updates from retail to kol/wholesale on approval
- [ ] User type remains retail on rejection
- [ ] User receives appropriate notifications

### User Experience Testing
- [ ] Users can view their applications
- [ ] Users can cancel pending applications
- [ ] Proper error messages for validation
- [ ] Proper success messages for each flow

## Next Steps

1. Run database synchronization: `npm run entity:sync`
2. Test API endpoints with Postman/similar tool
3. Implement frontend changes to support new registration forms
4. Add email notifications for application status changes
5. Add admin dashboard for reviewing applications

import { Test, TestingModule } from '@nestjs/testing';
import { QrCodeService } from '../src/modules/point-management/services/qrcode.service.js';
import { ProductQrCodeRepository } from '../src/entities/index.js';
import { EntityManager } from '@mikro-orm/mysql';
import { DrceuticsService } from '../src/modules/point-management/services/drceutics.service.js';

describe('QrCodeService - QR Code Generation', () => {
  let service: QrCodeService;
  let mockProductQrRepository: jest.Mocked<ProductQrCodeRepository>;
  let mockEntityManager: jest.Mocked<EntityManager>;

  beforeEach(async () => {
    // Create mocks
    mockProductQrRepository = {
      findAll: jest.fn(),
      create: jest.fn(),
    } as any;

    mockEntityManager = {
      persistAndFlush: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QrCodeService,
        {
          provide: ProductQrCodeRepository,
          useValue: mockProductQrRepository,
        },
        {
          provide: EntityManager,
          useValue: mockEntityManager,
        },
        {
          provide: DrceuticsService,
          useValue: {},
        },
        {
          provide: 'ProductPointRepository',
          useValue: {},
        },
        {
          provide: 'UserPointRepository',
          useValue: {},
        },
        {
          provide: 'UserPointHistoryRepository',
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<QrCodeService>(QrCodeService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should generate unique QR codes with correct format', async () => {
    // Mock existing data
    mockProductQrRepository.findAll.mockResolvedValueOnce([]);
    mockProductQrRepository.create.mockImplementation((data) => data as any);
    mockEntityManager.persistAndFlush.mockResolvedValue(undefined);

    // Test with small count for testing
    const result = await service.generateQrCodes(5);

    expect(result).toBeInstanceOf(Buffer);
    expect(mockProductQrRepository.findAll).toHaveBeenCalledTimes(2); // Once for QR codes, once for scratch codes
    expect(mockEntityManager.persistAndFlush).toHaveBeenCalled();
  });

  it('should generate codes with correct prefix', () => {
    // Test the private method indirectly by checking the service exists
    expect(service).toBeDefined();
    // Note: In a real test, you might want to make the generation methods public for testing
    // or test them through the public interface
  });
});

{"compilerOptions": {"target": "es2023", "outDir": "dist", "module": "nodenext", "moduleResolution": "nodenext", "incremental": true, "declaration": true, "newLine": "lf", "strict": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "forceConsistentCasingInFileNames": true, "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedSideEffectImports": true, "noUnusedLocals": true, "noUnusedParameters": true, "useDefineForClassFields": false, "verbatimModuleSyntax": true, "removeComments": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "skipLibCheck": true}, "include": ["typings/global.d.ts", "src/**/*", "test/**/*", "bin/**/*"], "exclude": ["node_modules"]}
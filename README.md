ở đây mới có 1 phư<PERSON>ng thức thanh to<PERSON>Config quân phải làm tiếp


# 2. <PERSON><PERSON><PERSON> nhập vào Amazon ECR
  * Chạy lệnh sau để đăng nhập vào Amazon ECR:
    * example:
    ```sh
    aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin xxxx.dkr.ecr.ap-southeast-1.amazonaws.com
    ```
    * ************ là AWS account ID của bạn.
    * ap-southeast-1 là region của bạn.

    * Thực thi lệnh sau để đăng nhập vào Amazon ECR:
    ```sh
      aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-southeast-1.amazonaws.com
    ```
# 3. Build image
  * Build image:
    ```bash
       docker build -t <repository>:<tag> .
    ```
  * <repository>: Tên của image hoặc repository mà bạn muốn gắn tag.
  * <tag>: Tag của image.

 docker buildx build --platform linux/amd64 -t ticketing-payment:1.0.6 --load . 
## 5. Push image lên registry Amazon ECR
  ```sh 
  docker tag worker-zone:1.0.0 <account_id>.dkr.ecr.<region>.amazonaws.com/worker-zone:1.0.0
  docker push <account_id>.dkr.ecr.<region>.amazonaws.com/worker-zone:1.0.0
  ```

  ```sh
  docker buildx build --platform linux/amd64 -t laboratory:1.0.7 --load . 
  docker tag laboratory:1.0.7 ************.dkr.ecr.ap-southeast-1.amazonaws.com/laboratory:1.0.7
  docker push ************.dkr.ecr.ap-southeast-1.amazonaws.com/laboratory:1.0.7
  ```

```sh
 docker buildx build --platform linux/amd64 -t laboratory:1.0.0 --load . 
 docker tag laboratory:1.0.0 ************.dkr.ecr.ap-southeast-1.amazonaws.com/laboratory:1.0.0
 docker push ************.dkr.ecr.ap-southeast-1.amazonaws.com/laboratory:1.0.0
```